#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack唯一ID生成器

解决图片同名冲突问题，为每个图片生成唯一标识

作者: PS画布修复团队
日期: 2024-12-19
版本: 第二阶段实现
"""

import os
import time
import hashlib
from typing import Dict, Any, Set, Optional, List
from dataclasses import dataclass, field


@dataclass
class ImageIdentity:
    """图片身份信息"""
    unique_id: str = ""
    layer_name: str = ""
    original_name: str = ""
    content_hash: str = ""
    session_id: str = ""
    sequence_number: int = 0
    creation_time: float = field(default_factory=time.time)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'unique_id': self.unique_id,
            'layer_name': self.layer_name,
            'original_name': self.original_name,
            'content_hash': self.content_hash,
            'session_id': self.session_id,
            'sequence_number': self.sequence_number,
            'creation_time': self.creation_time
        }


class SessionManager:
    """会话管理器"""

    def __init__(self):
        self.session_id = self._generate_session_id()
        self.start_time = time.time()

    def _generate_session_id(self) -> str:
        """生成会话ID"""
        return time.strftime("%Y%m%d_%H%M%S")

    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        return {
            'session_id': self.session_id,
            'start_time': self.start_time,
            'duration': time.time() - self.start_time
        }


class ContentHasher:
    """内容哈希生成器"""

    @staticmethod
    def generate_image_hash(image_info: Dict[str, Any]) -> str:
        """
        生成图片内容哈希

        Args:
            image_info: 图片信息字典

        Returns:
            str: 4位哈希值
        """
        # 构建哈希内容
        content_parts = [
            str(image_info.get('path', '')),
            str(image_info.get('width', 0)),
            str(image_info.get('height', 0)),
            str(image_info.get('size', 0))  # 文件大小（如果有）
        ]

        content = '|'.join(content_parts)
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:4]

    @staticmethod
    def generate_path_hash(file_path: str) -> str:
        """
        生成路径哈希

        Args:
            file_path: 文件路径

        Returns:
            str: 4位路径哈希
        """
        return hashlib.md5(file_path.encode('utf-8')).hexdigest()[:4]


class SequenceCounter:
    """序号计数器"""

    def __init__(self):
        self.counter = 0
        self.used_numbers = set()

    def get_next_number(self) -> int:
        """获取下一个序号"""
        self.counter += 1

        # 确保序号唯一
        while self.counter in self.used_numbers:
            self.counter += 1

        self.used_numbers.add(self.counter)
        return self.counter

    def reserve_number(self, number: int) -> bool:
        """预留序号"""
        if number in self.used_numbers:
            return False

        self.used_numbers.add(number)
        return True

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'current_counter': self.counter,
            'used_count': len(self.used_numbers),
            'max_used': max(self.used_numbers) if self.used_numbers else 0
        }


class LayerNameFormatter:
    """图层名称格式化器"""

    @staticmethod
    def format_layer_name(unique_id: str, original_name: str,
                         max_length: int = 50) -> str:
        """
        格式化图层名称

        Args:
            unique_id: 唯一ID
            original_name: 原始文件名
            max_length: 最大长度

        Returns:
            str: 格式化的图层名称
        """
        # 提取文件名（不含扩展名）
        base_name = os.path.splitext(original_name)[0]

        # 清理非法字符
        base_name = LayerNameFormatter._clean_invalid_chars(base_name)

        # 构建图层名称
        layer_name = f"{unique_id}_{base_name}"

        # 长度限制
        if len(layer_name) > max_length:
            # 截断原始名称部分
            available_length = max_length - len(unique_id) - 1
            if available_length > 0:
                base_name = base_name[:available_length]
                layer_name = f"{unique_id}_{base_name}"
            else:
                layer_name = unique_id

        return layer_name

    @staticmethod
    def _clean_invalid_chars(name: str) -> str:
        """
        清理文件名中的非法字符

        Args:
            name: 原始名称

        Returns:
            str: 清理后的名称
        """
        # 定义非法字符映射
        invalid_chars = {
            '/': '_',
            '\\': '_',
            ':': '_',
            '*': '_',
            '?': '_',
            '"': '_',
            '<': '_',
            '>': '_',
            '|': '_'
        }

        # 替换非法字符
        cleaned_name = name
        for invalid_char, replacement in invalid_chars.items():
            cleaned_name = cleaned_name.replace(invalid_char, replacement)

        # 移除连续的下划线
        while '__' in cleaned_name:
            cleaned_name = cleaned_name.replace('__', '_')

        # 移除开头和结尾的下划线
        cleaned_name = cleaned_name.strip('_')

        # 如果清理后为空，使用默认名称
        if not cleaned_name:
            cleaned_name = 'image'

        return cleaned_name

    @staticmethod
    def validate_layer_name(layer_name: str) -> bool:
        """
        验证图层名称有效性

        Args:
            layer_name: 图层名称

        Returns:
            bool: 是否有效
        """
        # 检查长度
        if len(layer_name) == 0 or len(layer_name) > 255:
            return False

        # 检查非法字符
        invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in invalid_chars:
            if char in layer_name:
                return False

        return True


class RectPackUniqueIDGenerator:
    """RectPack唯一ID生成器"""

    def __init__(self):
        self.session_manager = SessionManager()
        self.content_hasher = ContentHasher()
        self.sequence_counter = SequenceCounter()
        self.layer_formatter = LayerNameFormatter()

        # 存储已生成的ID
        self.generated_ids: Set[str] = set()
        self.generated_identities: Dict[str, ImageIdentity] = {}

        # 统计信息
        self.generation_count = 0
        self.collision_count = 0

    def generate_unique_id(self, image_info: Dict[str, Any]) -> str:
        """
        生成唯一ID

        Args:
            image_info: 图片信息字典

        Returns:
            str: 唯一ID
        """
        # 获取组件
        session_id = self.session_manager.session_id
        sequence_num = self.sequence_counter.get_next_number()
        content_hash = self.content_hasher.generate_image_hash(image_info)

        # 构建ID
        unique_id = f"RP_{session_id}_{sequence_num:03d}_{content_hash}"

        # 检查冲突
        while unique_id in self.generated_ids:
            self.collision_count += 1
            sequence_num = self.sequence_counter.get_next_number()
            unique_id = f"RP_{session_id}_{sequence_num:03d}_{content_hash}"

        # 记录ID
        self.generated_ids.add(unique_id)
        self.generation_count += 1

        return unique_id

    def generate_layer_name(self, unique_id: str, original_name: str) -> str:
        """
        生成图层名称

        Args:
            unique_id: 唯一ID
            original_name: 原始文件名

        Returns:
            str: 图层名称
        """
        layer_name = self.layer_formatter.format_layer_name(unique_id, original_name)

        # 验证名称
        if not self.layer_formatter.validate_layer_name(layer_name):
            # 如果无效，使用纯ID
            layer_name = unique_id

        return layer_name

    def create_image_identity(self, image_info: Dict[str, Any]) -> ImageIdentity:
        """
        创建图片身份信息

        Args:
            image_info: 图片信息字典

        Returns:
            ImageIdentity: 图片身份信息
        """
        # 生成唯一ID
        unique_id = self.generate_unique_id(image_info)

        # 生成图层名称
        original_name = image_info.get('name', 'unknown')
        layer_name = self.generate_layer_name(unique_id, original_name)

        # 创建身份信息
        identity = ImageIdentity(
            unique_id=unique_id,
            layer_name=layer_name,
            original_name=original_name,
            content_hash=self.content_hasher.generate_image_hash(image_info),
            session_id=self.session_manager.session_id,
            sequence_number=self.sequence_counter.counter
        )

        # 存储身份信息
        self.generated_identities[unique_id] = identity

        return identity

    def get_identity_by_id(self, unique_id: str) -> Optional[ImageIdentity]:
        """
        根据ID获取身份信息

        Args:
            unique_id: 唯一ID

        Returns:
            ImageIdentity: 身份信息，如果不存在返回None
        """
        return self.generated_identities.get(unique_id)

    def find_identity_by_original_name(self, original_name: str) -> List[ImageIdentity]:
        """
        根据原始名称查找身份信息

        Args:
            original_name: 原始文件名

        Returns:
            List[ImageIdentity]: 匹配的身份信息列表
        """
        matches = []
        for identity in self.generated_identities.values():
            if identity.original_name == original_name:
                matches.append(identity)
        return matches

    def get_statistics(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        return {
            'session_info': self.session_manager.get_session_info(),
            'generation_count': self.generation_count,
            'collision_count': self.collision_count,
            'unique_ids_count': len(self.generated_ids),
            'identities_count': len(self.generated_identities),
            'sequence_stats': self.sequence_counter.get_statistics()
        }

    def export_identities(self) -> List[Dict[str, Any]]:
        """导出所有身份信息"""
        return [identity.to_dict() for identity in self.generated_identities.values()]

    def clear_session(self):
        """清空当前会话"""
        self.generated_ids.clear()
        self.generated_identities.clear()
        self.generation_count = 0
        self.collision_count = 0
        self.sequence_counter = SequenceCounter()
        self.session_manager = SessionManager()


# 导出主要类
__all__ = [
    'ImageIdentity',
    'RectPackUniqueIDGenerator',
    'SessionManager',
    'ContentHasher',
    'SequenceCounter',
    'LayerNameFormatter'
]

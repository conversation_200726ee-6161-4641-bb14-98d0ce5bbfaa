#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试可视化器模块

提供测试模式下的图片布局可视化功能，生成彩色方块图片用于测试和调试
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# 配置日志
log = logging.getLogger(__name__)

class TestVisualizer:
    """测试可视化器
    
    用于在测试模式下生成彩色方块图片，替代实际图片进行布局测试
    """
    
    def __init__(self, output_folder: str):
        """初始化测试可视化器
        
        Args:
            output_folder: 输出文件夹路径
        """
        self.output_folder = output_folder
        self.ensure_output_folder()
    
    def ensure_output_folder(self):
        """确保输出文件夹存在"""
        try:
            if not os.path.exists(self.output_folder):
                os.makedirs(self.output_folder, exist_ok=True)
                log.info(f"创建输出文件夹: {self.output_folder}")
        except Exception as e:
            log.error(f"创建输出文件夹失败: {str(e)}")
    
    def generate_visualization(self, arranged_images: List[Dict[str, Any]], 
                             canvas_width_px: int, canvas_height_px: int, 
                             canvas_name: str) -> Optional[str]:
        """生成可视化图片
        
        Args:
            arranged_images: 排列好的图片列表
            canvas_width_px: 画布宽度（像素）
            canvas_height_px: 画布高度（像素）
            canvas_name: 画布名称
            
        Returns:
            str: 生成的图片路径，失败时返回None
        """
        try:
            # 导入PIL库
            try:
                from PIL import Image, ImageDraw, ImageFont
            except ImportError:
                log.error("PIL库不可用，无法生成可视化图片")
                return None
            
            # 计算缩放比例（限制最大尺寸为2000px）
            max_size = 2000
            scale_factor = 1.0
            if canvas_width_px > max_size or canvas_height_px > max_size:
                scale_factor = min(max_size / canvas_width_px, max_size / canvas_height_px)
            
            # 计算实际画布尺寸
            actual_width = int(canvas_width_px * scale_factor)
            actual_height = int(canvas_height_px * scale_factor)
            
            # 创建画布
            image = Image.new('RGB', (actual_width, actual_height), (255, 255, 255))
            draw = ImageDraw.Draw(image)
            
            # 绘制画布边框
            draw.rectangle([0, 0, actual_width-1, actual_height-1], 
                         outline=(0, 0, 0), width=2)
            
            # 定义颜色映射
            color_map = {
                'A': (255, 102, 102),  # 红色 - A类图片
                'B': (102, 255, 102),  # 绿色 - B类图片  
                'C': (102, 102, 255),  # 蓝色 - C类图片
                'default': (128, 128, 128)  # 灰色 - 默认
            }
            
            # 绘制每个图片
            for i, img in enumerate(arranged_images):
                try:
                    # 获取图片信息
                    x = int(img.get('x', 0) * scale_factor)
                    y = int(img.get('y', 0) * scale_factor)
                    width = int(img.get('width', 0) * scale_factor)
                    height = int(img.get('height', 0) * scale_factor)
                    name = img.get('name', f'Image_{i+1}')
                    image_class = img.get('image_class', 'default')
                    
                    # 确保尺寸至少为1像素
                    width = max(1, width)
                    height = max(1, height)
                    
                    # 获取颜色
                    color = color_map.get(image_class, color_map['default'])
                    
                    # 绘制矩形
                    draw.rectangle([x, y, x + width, y + height], 
                                 fill=color, outline=(0, 0, 0), width=1)
                    
                    # 绘制文本（如果空间足够）
                    if width > 30 and height > 15:
                        try:
                            # 尝试加载字体
                            try:
                                font = ImageFont.truetype("arial.ttf", 8)
                            except:
                                font = ImageFont.load_default()
                            
                            # 绘制图片名称
                            text_x = x + width // 2
                            text_y = y + height // 2
                            draw.text((text_x, text_y), name, 
                                    fill=(255, 255, 255), font=font, anchor='mm')
                        except Exception:
                            pass  # 忽略字体错误
                            
                except Exception as e:
                    log.warning(f"绘制图片 {i} 时出错: {str(e)}")
                    continue
            
            # 保存图片
            image_path = os.path.join(self.output_folder, f"{canvas_name}.png")
            image.save(image_path, 'PNG')
            
            # 保存数据文件
            data_path = os.path.join(self.output_folder, f"{canvas_name}_data.json")
            self._save_data_file(data_path, arranged_images, canvas_width_px, 
                               canvas_height_px, canvas_name)
            
            log.info(f"测试可视化图片已生成: {image_path}")
            return image_path
            
        except Exception as e:
            log.error(f"生成可视化图片失败: {str(e)}")
            return None
    
    def _save_data_file(self, data_path: str, arranged_images: List[Dict[str, Any]], 
                       canvas_width_px: int, canvas_height_px: int, canvas_name: str):
        """保存数据文件
        
        Args:
            data_path: 数据文件路径
            arranged_images: 排列好的图片列表
            canvas_width_px: 画布宽度
            canvas_height_px: 画布高度
            canvas_name: 画布名称
        """
        try:
            data = {
                "canvas_info": {
                    "width": canvas_width_px,
                    "height": canvas_height_px,
                    "name": canvas_name
                },
                "arranged_images": arranged_images,
                "generation_time": datetime.now().isoformat(),
                "total_images": len(arranged_images)
            }
            
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            log.info(f"数据文件已保存: {data_path}")
            
        except Exception as e:
            log.error(f"保存数据文件失败: {str(e)}")
    
    def generate_simple_visualization(self, width: int, height: int, 
                                    name: str = "test_canvas") -> Optional[str]:
        """生成简单的可视化图片（仅画布）
        
        Args:
            width: 画布宽度
            height: 画布高度
            name: 画布名称
            
        Returns:
            str: 生成的图片路径，失败时返回None
        """
        try:
            from PIL import Image, ImageDraw
            
            # 限制最大尺寸
            max_size = 1000
            scale_factor = 1.0
            if width > max_size or height > max_size:
                scale_factor = min(max_size / width, max_size / height)
            
            actual_width = int(width * scale_factor)
            actual_height = int(height * scale_factor)
            
            # 创建画布
            image = Image.new('RGB', (actual_width, actual_height), (255, 255, 255))
            draw = ImageDraw.Draw(image)
            
            # 绘制边框
            draw.rectangle([0, 0, actual_width-1, actual_height-1], 
                         outline=(0, 0, 0), width=2)
            
            # 保存图片
            image_path = os.path.join(self.output_folder, f"{name}.png")
            image.save(image_path, 'PNG')
            
            log.info(f"简单可视化图片已生成: {image_path}")
            return image_path
            
        except Exception as e:
            log.error(f"生成简单可视化图片失败: {str(e)}")
            return None


def create_test_visualizer(output_folder: str) -> TestVisualizer:
    """创建测试可视化器实例
    
    Args:
        output_folder: 输出文件夹路径
        
    Returns:
        TestVisualizer: 测试可视化器实例
    """
    return TestVisualizer(output_folder)


# 兼容性函数，用于向后兼容
def generate_test_visualization(arranged_images: List[Dict[str, Any]], 
                              canvas_width_px: int, canvas_height_px: int, 
                              canvas_name: str, output_folder: str) -> Optional[str]:
    """生成测试可视化图片（兼容性函数）
    
    Args:
        arranged_images: 排列好的图片列表
        canvas_width_px: 画布宽度（像素）
        canvas_height_px: 画布高度（像素）
        canvas_name: 画布名称
        output_folder: 输出文件夹路径
        
    Returns:
        str: 生成的图片路径，失败时返回None
    """
    visualizer = TestVisualizer(output_folder)
    return visualizer.generate_visualization(arranged_images, canvas_width_px, 
                                           canvas_height_px, canvas_name)

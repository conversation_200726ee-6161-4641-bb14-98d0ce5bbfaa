#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片尺寸预验证器

在算法计算前验证实际图片尺寸，确保测试和正式环境使用相同的尺寸数据

作者: PS画布修复团队
日期: 2024-12-19
版本: 第三阶段优化
"""

import os
import time
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass, field
from PIL import Image
import logging


@dataclass
class ImageSizeInfo:
    """图片尺寸信息"""
    file_path: str = ""
    original_width: int = 0
    original_height: int = 0
    expected_width: int = 0
    expected_height: int = 0
    actual_width: int = 0
    actual_height: int = 0
    size_match: bool = False
    file_exists: bool = False
    file_size: int = 0
    validation_time: float = field(default_factory=time.time)
    errors: List[str] = field(default_factory=list)
    
    def get_size_difference(self) -> Tuple[int, int]:
        """获取尺寸差异"""
        width_diff = self.actual_width - self.expected_width
        height_diff = self.actual_height - self.expected_height
        return width_diff, height_diff
    
    def get_size_ratio(self) -> Tu<PERSON>[float, float]:
        """获取尺寸比例"""
        if self.expected_width > 0 and self.expected_height > 0:
            width_ratio = self.actual_width / self.expected_width
            height_ratio = self.actual_height / self.expected_height
            return width_ratio, height_ratio
        return 1.0, 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'file_path': self.file_path,
            'original_width': self.original_width,
            'original_height': self.original_height,
            'expected_width': self.expected_width,
            'expected_height': self.expected_height,
            'actual_width': self.actual_width,
            'actual_height': self.actual_height,
            'size_match': self.size_match,
            'file_exists': self.file_exists,
            'file_size': self.file_size,
            'validation_time': self.validation_time,
            'errors': self.errors,
            'size_difference': self.get_size_difference(),
            'size_ratio': self.get_size_ratio()
        }


@dataclass
class ValidationConfig:
    """验证配置"""
    tolerance_pixels: int = 5  # 像素容差
    tolerance_percent: float = 5.0  # 百分比容差
    enable_auto_correction: bool = True  # 启用自动修正
    cache_results: bool = True  # 缓存验证结果
    max_cache_size: int = 1000  # 最大缓存数量
    validate_file_existence: bool = True  # 验证文件存在性
    validate_file_readability: bool = True  # 验证文件可读性
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'tolerance_pixels': self.tolerance_pixels,
            'tolerance_percent': self.tolerance_percent,
            'enable_auto_correction': self.enable_auto_correction,
            'cache_results': self.cache_results,
            'max_cache_size': self.max_cache_size,
            'validate_file_existence': self.validate_file_existence,
            'validate_file_readability': self.validate_file_readability
        }


class ImageSizeCache:
    """图片尺寸缓存"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, ImageSizeInfo] = {}
        self.access_times: Dict[str, float] = {}
    
    def get(self, file_path: str) -> Optional[ImageSizeInfo]:
        """获取缓存的尺寸信息"""
        if file_path in self.cache:
            self.access_times[file_path] = time.time()
            return self.cache[file_path]
        return None
    
    def put(self, file_path: str, size_info: ImageSizeInfo):
        """存储尺寸信息到缓存"""
        # 如果缓存已满，移除最旧的条目
        if len(self.cache) >= self.max_size:
            oldest_path = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[oldest_path]
            del self.access_times[oldest_path]
        
        self.cache[file_path] = size_info
        self.access_times[file_path] = time.time()
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'cache_size': len(self.cache),
            'max_size': self.max_size,
            'hit_rate': 0.0  # 需要在使用中统计
        }


class ImageSizeValidator:
    """图片尺寸验证器"""
    
    def __init__(self, config: ValidationConfig = None):
        self.config = config or ValidationConfig()
        self.cache = ImageSizeCache(self.config.max_cache_size) if self.config.cache_results else None
        self.validation_history: List[ImageSizeInfo] = []
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
    
    def _get_actual_image_size(self, file_path: str) -> Tuple[int, int, List[str]]:
        """
        获取图片的实际尺寸
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            Tuple[int, int, List[str]]: (宽度, 高度, 错误信息)
        """
        errors = []
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                errors.append(f"文件不存在: {file_path}")
                return 0, 0, errors
            
            # 检查文件是否可读
            if not os.access(file_path, os.R_OK):
                errors.append(f"文件不可读: {file_path}")
                return 0, 0, errors
            
            # 使用PIL获取图片尺寸
            with Image.open(file_path) as img:
                width, height = img.size
                return width, height, errors
                
        except Exception as e:
            errors.append(f"读取图片尺寸失败: {str(e)}")
            return 0, 0, errors
    
    def _check_size_tolerance(self, expected: int, actual: int) -> bool:
        """
        检查尺寸是否在容差范围内
        
        Args:
            expected: 期望尺寸
            actual: 实际尺寸
            
        Returns:
            bool: 是否在容差范围内
        """
        # 像素容差检查
        pixel_diff = abs(actual - expected)
        if pixel_diff <= self.config.tolerance_pixels:
            return True
        
        # 百分比容差检查
        if expected > 0:
            percent_diff = (pixel_diff / expected) * 100
            if percent_diff <= self.config.tolerance_percent:
                return True
        
        return False
    
    def validate_image_size(self, image_data: Dict[str, Any]) -> ImageSizeInfo:
        """
        验证单个图片尺寸
        
        Args:
            image_data: 图片数据字典
            
        Returns:
            ImageSizeInfo: 验证结果
        """
        file_path = image_data.get('path', '')
        expected_width = int(image_data.get('width', 0))
        expected_height = int(image_data.get('height', 0))
        
        # 检查缓存
        if self.cache:
            cached_info = self.cache.get(file_path)
            if cached_info:
                self.cache_hits += 1
                # 更新期望尺寸（可能有变化）
                cached_info.expected_width = expected_width
                cached_info.expected_height = expected_height
                cached_info.size_match = (
                    self._check_size_tolerance(expected_width, cached_info.actual_width) and
                    self._check_size_tolerance(expected_height, cached_info.actual_height)
                )
                return cached_info
            else:
                self.cache_misses += 1
        
        # 创建尺寸信息对象
        size_info = ImageSizeInfo(
            file_path=file_path,
            expected_width=expected_width,
            expected_height=expected_height
        )
        
        # 检查文件存在性
        size_info.file_exists = os.path.exists(file_path)
        if size_info.file_exists:
            try:
                size_info.file_size = os.path.getsize(file_path)
            except:
                size_info.file_size = 0
        
        # 获取实际尺寸
        if size_info.file_exists:
            actual_width, actual_height, errors = self._get_actual_image_size(file_path)
            size_info.actual_width = actual_width
            size_info.actual_height = actual_height
            size_info.original_width = actual_width
            size_info.original_height = actual_height
            size_info.errors.extend(errors)
            
            # 检查尺寸匹配
            if not errors:
                width_match = self._check_size_tolerance(expected_width, actual_width)
                height_match = self._check_size_tolerance(expected_height, actual_height)
                size_info.size_match = width_match and height_match
                
                if not size_info.size_match:
                    width_diff, height_diff = size_info.get_size_difference()
                    size_info.errors.append(
                        f"尺寸不匹配: 期望{expected_width}x{expected_height}, "
                        f"实际{actual_width}x{actual_height}, "
                        f"差异({width_diff}, {height_diff})"
                    )
        else:
            size_info.errors.append("文件不存在，无法验证尺寸")
        
        # 存储到缓存
        if self.cache:
            self.cache.put(file_path, size_info)
        
        # 记录到历史
        self.validation_history.append(size_info)
        
        return size_info
    
    def batch_validate_sizes(self, images_data: List[Dict[str, Any]]) -> List[ImageSizeInfo]:
        """
        批量验证图片尺寸
        
        Args:
            images_data: 图片数据列表
            
        Returns:
            List[ImageSizeInfo]: 验证结果列表
        """
        results = []
        
        for image_data in images_data:
            size_info = self.validate_image_size(image_data)
            results.append(size_info)
        
        return results
    
    def correct_image_sizes(self, images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        修正图片尺寸数据
        
        Args:
            images_data: 图片数据列表
            
        Returns:
            List[Dict]: 修正后的图片数据列表
        """
        corrected_data = []
        
        for image_data in images_data:
            size_info = self.validate_image_size(image_data)
            
            # 复制原始数据
            corrected = image_data.copy()
            
            # 添加验证信息
            corrected['size_validation'] = size_info.to_dict()
            
            # 如果启用自动修正且尺寸不匹配
            if self.config.enable_auto_correction and not size_info.size_match and size_info.file_exists:
                if size_info.actual_width > 0 and size_info.actual_height > 0:
                    # 使用实际尺寸
                    corrected['width'] = size_info.actual_width
                    corrected['height'] = size_info.actual_height
                    corrected['size_corrected'] = True
                    
                    self.logger.info(
                        f"自动修正图片尺寸: {size_info.file_path} "
                        f"{size_info.expected_width}x{size_info.expected_height} -> "
                        f"{size_info.actual_width}x{size_info.actual_height}"
                    )
                else:
                    corrected['size_corrected'] = False
                    corrected['size_correction_failed'] = True
            else:
                corrected['size_corrected'] = False
            
            corrected_data.append(corrected)
        
        return corrected_data
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        if not self.validation_history:
            return {'validated_count': 0}
        
        total_count = len(self.validation_history)
        matched_count = sum(1 for info in self.validation_history if info.size_match)
        file_exists_count = sum(1 for info in self.validation_history if info.file_exists)
        error_count = sum(1 for info in self.validation_history if info.errors)
        
        # 计算尺寸差异统计
        width_diffs = []
        height_diffs = []
        
        for info in self.validation_history:
            if info.file_exists and info.actual_width > 0 and info.actual_height > 0:
                width_diff, height_diff = info.get_size_difference()
                width_diffs.append(abs(width_diff))
                height_diffs.append(abs(height_diff))
        
        # 缓存统计
        cache_stats = {}
        if self.cache:
            total_requests = self.cache_hits + self.cache_misses
            cache_stats = {
                'cache_enabled': True,
                'cache_hits': self.cache_hits,
                'cache_misses': self.cache_misses,
                'cache_hit_rate': self.cache_hits / total_requests * 100 if total_requests > 0 else 0,
                'cache_size': len(self.cache.cache)
            }
        else:
            cache_stats = {'cache_enabled': False}
        
        return {
            'validated_count': total_count,
            'matched_count': matched_count,
            'file_exists_count': file_exists_count,
            'error_count': error_count,
            'match_rate': matched_count / total_count * 100 if total_count > 0 else 0,
            'file_exists_rate': file_exists_count / total_count * 100 if total_count > 0 else 0,
            'size_differences': {
                'width_max_diff': max(width_diffs) if width_diffs else 0,
                'height_max_diff': max(height_diffs) if height_diffs else 0,
                'width_avg_diff': sum(width_diffs) / len(width_diffs) if width_diffs else 0,
                'height_avg_diff': sum(height_diffs) / len(height_diffs) if height_diffs else 0
            },
            'cache_statistics': cache_stats,
            'config': self.config.to_dict()
        }
    
    def clear_history(self):
        """清空验证历史"""
        self.validation_history.clear()
        if self.cache:
            self.cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0


class TestModeImageSizeProcessor:
    """测试模式图片尺寸处理器"""
    
    def __init__(self, validator: ImageSizeValidator):
        self.validator = validator
    
    def process_for_test_mode(self, images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为测试模式处理图片尺寸
        
        Args:
            images_data: 图片数据列表
            
        Returns:
            List[Dict]: 处理后的图片数据列表
        """
        return self.validator.correct_image_sizes(images_data)


class ProductionModeImageSizeProcessor:
    """正式环境图片尺寸处理器"""
    
    def __init__(self, validator: ImageSizeValidator):
        self.validator = validator
    
    def process_for_production_mode(self, images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为正式环境处理图片尺寸
        
        Args:
            images_data: 图片数据列表
            
        Returns:
            List[Dict]: 处理后的图片数据列表
        """
        # 使用完全相同的处理逻辑
        return self.validator.correct_image_sizes(images_data)


# 导出主要类
__all__ = [
    'ImageSizeInfo',
    'ValidationConfig',
    'ImageSizeCache',
    'ImageSizeValidator',
    'TestModeImageSizeProcessor',
    'ProductionModeImageSizeProcessor'
]

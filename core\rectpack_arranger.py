#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于RectPack的优化图片排列器 - 俄罗斯方块式紧密排列
实现真正的俄罗斯方块式紧密排列，专门优化C类图片的空间利用率

核心优化特性：
1. 天际线算法(Skyline Algorithm)：实现真正的俄罗斯方块式堆叠
2. 智能间隙填充：消除不必要的水平和垂直间隙
3. 图片组合优化：寻找完美填充画布宽度的图片组合
4. 错位垂直排列：类似俄罗斯方块的紧密堆叠模式
5. 旋转优化：当水平放置失败时尝试90度旋转
6. 测试模式支持：PIL彩色方块生成和详细文档输出
7. 正式模式支持：Photoshop自动化脚本调用
8. 性能优化：极速排列算法，避免用户等待

遵循原则：
- DRY原则：避免重复代码
- KISS原则：保持简单直接
- SOLID原则：单一职责，开闭原则
- YAGNI原则：只实现需要的功能
- 第一性原理：基于算法和策略，不针对特殊尺寸优化
"""

import logging
# import time  # 已替换为统一的时间处理工具
# import random  # 暂未使用
# import math  # 暂未使用
from typing import Dict, List, Tuple, Any, Optional
try:
    from rectpack import newPacker, SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO
    from rectpack import PackerBNF, PackerBFF, PackerBBF  # 修复导入路径
    RECTPACK_AVAILABLE = True

    # 创建兼容的BinPack类
    class BinPack:
        BNF = 0
        BFF = 1
        BBF = 2
        BinBestAreaFit = 0
except ImportError:
    # 如果rectpack不可用，定义一些默认值
    RECTPACK_AVAILABLE = False
    SORT_AREA = 0
    SORT_PERI = 1
    SORT_DIFF = 2
    SORT_SSIDE = 3
    SORT_LSIDE = 4
    SORT_RATIO = 5

    class BinPack:
        BNF = 0
        BFF = 1
        BBF = 2
        BinBestAreaFit = 0

# 导入抽象基类
from core.abstract_packer import AbstractPacker

# 导入统一处理接口
try:
    from core.unified_processor import (
        ImagePlacementData,
        UnifiedImageProcessor,
        create_unified_placement_data
    )
    UNIFIED_PROCESSOR_AVAILABLE = True
except ImportError as e:
    UNIFIED_PROCESSOR_AVAILABLE = False
    logging.warning(f"统一处理接口不可用: {str(e)}")

    # 创建占位类
    class ImagePlacementData:
        pass
    class UnifiedImageProcessor:
        pass

# 导入唯一ID生成器
try:
    from core.unique_id_generator import (
        RectPackUniqueIDGenerator,
        ImageIdentity
    )
    UNIQUE_ID_GENERATOR_AVAILABLE = True
except ImportError as e:
    UNIQUE_ID_GENERATOR_AVAILABLE = False
    logging.warning(f"唯一ID生成器不可用: {str(e)}")

    # 创建占位类
    class RectPackUniqueIDGenerator:
        pass
    class ImageIdentity:
        pass

# 导入matplotlib测试模式工具
try:
    from .rectpack_matplotlib_utils import (
        convert_cm_to_px_data,
        create_container_config_with_expansion,
        validate_px_data,
        calculate_layout_statistics_px,
        create_matplotlib_test_mode,
        generate_matplotlib_documentation
    )
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    MATPLOTLIB_AVAILABLE = False
    logging.warning(f"matplotlib工具模块不可用: {str(e)}")

# 配置日志
log = logging.getLogger(__name__)

class RectPackArranger(AbstractPacker):
    """
    基于RectPack的统一图片排列器

    使用rectpack算法实现最优的画布空间利用率，替换现有的复杂分类逻辑
    """

    def __init__(self, container_width: int, image_spacing: int = 0, max_height: int = 0,
                 log_signal=None, is_last_canvas=False, canvas_truncation_handler=None):
        """
        初始化RectPack排列器

        Args:
            container_width: 容器宽度（像素）
            image_spacing: 图片间距（像素）
            max_height: 最大高度限制（0表示无限制）
            log_signal: 日志信号，用于向UI发送日志
            is_last_canvas: 是否是最后一个画布
            canvas_truncation_handler: 画布截断处理器
        """
        super().__init__(container_width, image_spacing, max_height)

        self.log_signal = log_signal
        self.is_last_canvas = is_last_canvas
        self.canvas_truncation_handler = canvas_truncation_handler

        # RectPack相关设置
        self.packer = None
        self.bin_width = container_width
        # 限制最大高度，避免画布过大导致性能问题
        if max_height > 0:
            # 直接使用传入的最大高度限制，不再使用硬编码的值
            self.bin_height = max_height
        else:
            # 如果没有高度限制，使用一个非常大的默认值
            self.bin_height = 999999999  # 无限高度

        # 算法参数 - 从配置加载
        self._load_algorithm_parameters()

        # 性能参数 - 从配置加载
        self._load_performance_parameters()

        # 优化参数 - 从配置加载
        self._load_optimization_parameters()

        # 性能统计
        self.placement_count = 0
        self.total_area = 0
        self.used_area = 0

        # 状态管理
        self.canvas_is_full = False
        self.current_max_height = 0

        # 性能优化设置
        self.batch_processing = True  # 开启批量处理模式
        self.fast_mode = False  # 快速模式，减少日志输出
        self.progress_interval = 50  # 进度报告间隔
        self.enable_detailed_logging = True  # 是否启用详细日志

        self._initialize_packer()

        # 初始化统一处理器
        if UNIFIED_PROCESSOR_AVAILABLE:
            self.unified_processor = UnifiedImageProcessor(
                canvas_width=container_width,
                canvas_height=max_height if max_height > 0 else 5000,
                ppi=72
            )
        else:
            self.unified_processor = None

        # 初始化唯一ID生成器
        if UNIQUE_ID_GENERATOR_AVAILABLE:
            self.id_generator = RectPackUniqueIDGenerator()
            if self.log_signal:
                self.log_signal.emit("✅ 唯一ID生成器已初始化")
        else:
            self.id_generator = None
            if self.log_signal:
                self.log_signal.emit("⚠️ 唯一ID生成器不可用，使用默认命名")

    def enable_fast_mode(self, enable: bool = True):
        """
        启用或禁用快速模式，用于大批量图片处理

        Args:
            enable: 是否启用快速模式
        """
        self.fast_mode = enable
        self.enable_detailed_logging = not enable
        if enable:
            self.progress_interval = 100  # 快速模式下减少进度报告频率
        else:
            self.progress_interval = 50

        if self.log_signal and not self.fast_mode:
            self.log_signal.emit(f"快速模式: {'\u5f00\u542f' if enable else '\u5173\u95ed'}")

    def _initialize_packer(self, silent=False):
        """初始化rectpack装箱器

        Args:
            silent: 是否静默初始化（不输出日志）
        """
        if not RECTPACK_AVAILABLE:
            if self.log_signal and not silent:
                self.log_signal.emit("警告: rectpack库不可用，将使用简化的排列算法")
            return

        # 使用配置参数初始化packer
        pack_algo_map = {
            0: 'MaxRectsBssf',  # Best Short Side Fit
            1: 'MaxRectsBaf',   # Best Area Fit
            2: 'MaxRectsBlsf',  # Best Long Side Fit
        }

        try:
            from rectpack import MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf
            pack_algo_class = {
                0: MaxRectsBssf,
                1: MaxRectsBaf,
                2: MaxRectsBlsf
            }.get(getattr(self, 'free_rect_choice', 0), MaxRectsBssf)
        except ImportError:
            from rectpack import MaxRectsBssf
            pack_algo_class = MaxRectsBssf

        self.packer = newPacker(
            mode=1,  # Offline mode - 批量处理模式
            bin_algo=getattr(self, 'bin_selection_strategy', 2),  # 使用配置的bin选择策略
            pack_algo=pack_algo_class,  # 使用配置的装箱算法
            sort_algo=self._create_optimized_sort_function(),  # 优化的排序函数
            rotation=self.rotation_enabled  # 使用配置的旋转设置
        )

        # 添加一个bin（容器）
        self.packer.add_bin(self.bin_width, self.bin_height)

    def _create_optimized_sort_function(self):
        """
        创建优化的排序函数，最大化画布利用率

        基于测试结果，按面积降序排列能获得最佳效果

        Returns:
            function: 排序函数
        """
        def optimized_sort(rect_list):
            """
            高级优化排序算法

            优先级：
            1. 面积大的图片优先（主要因素）
            2. 长宽比接近1:1的图片优先（更容易放置）
            3. 边长较大的图片优先（减少碎片）
            """
            return sorted(rect_list, key=lambda rect: (
                # 主要排序因子：面积（降序）
                -(rect[0] * rect[1]),

                # 次要因子：长宽比接近1:1的优先（降序）
                -min(rect[0] / max(rect[1], 1), rect[1] / max(rect[0], 1)),

                # 辅助因子：最大边长（降序）
                -max(rect[0], rect[1])
            ))

        return optimized_sort

    def _apply_advanced_optimizations(self):
        """
        应用高级优化策略

        包括：
        1. 智能旋转策略
        2. 空间利用优化
        3. 碎片最小化
        """
        # 启用高级旋转策略
        if self.rotation_enabled:
            self.rotation_priority = 0.8  # 旋转优先级
            self.rotation_threshold = 0.15  # 旋转阈值，当利用率提升超过15%时才旋转

        # 空间利用优化
        self.gap_filling_enabled = True  # 启用空隙填充
        self.compactness_factor = 0.9   # 紧密度因子

        # 碎片最小化
        self.fragment_penalty = 0.1     # 碎片惩罚因子

    def place_image(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        放置图片 - 主入口函数，分解为更小的模块

        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        if not RECTPACK_AVAILABLE:
            return self._simple_place_image(width, height, image_data)

        if self.canvas_is_full:
            return 0, 0, False

        # 第一步：预处理图片尺寸和旋转
        processed_dims = self._preprocess_image_dimensions(width, height)
        if processed_dims is None:
            return 0, 0, False

        width_with_spacing, height_with_spacing, was_rotated = processed_dims

        # 第二步：尝试放置图片
        placement_result = self._attempt_image_placement(width_with_spacing, height_with_spacing)
        if placement_result is None:
            self.canvas_is_full = True
            # 发送画布满信号，类似于俄罗斯方块算法
            if self.log_signal:
                self.log_signal.emit(f"画布已满：当前高度 {self.current_max_height}px 接近最大高度限制 {self.bin_height}px，需要创建新画布")
            return 0, 0, False

        x, y, actual_width, actual_height, test_packer = placement_result

        # 第三步：验证坐标有效性（新增的关键修复）
        is_valid, error_msg = self._validate_image_placement(x, y, actual_width, actual_height)
        if not is_valid:
            if self.log_signal:
                self.log_signal.emit(f"⚠️ 坐标验证失败: {error_msg}")
            # 如果坐标无效，标记为画布已满
            self.canvas_is_full = True
            return 0, 0, False

        # 第四步：更新状态和记录
        self._update_placement_state(x, y, actual_width, actual_height, was_rotated, image_data, test_packer)

        return x, y, True

    def _validate_image_placement(self, x: int, y: int, width: int, height: int) -> Tuple[bool, str]:
        """
        验证图片放置位置是否有效（新增的关键修复）

        Args:
            x: x坐标
            y: y坐标
            width: 宽度
            height: 高度

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        # 1. 边界检查
        if x < 0 or y < 0:
            return False, f"坐标不能为负数: ({x}, {y})"

        if x + width > self.bin_width:
            return False, f"图片超出画布右边界: {x + width} > {self.bin_width}"

        if self.bin_height > 0 and y + height > self.bin_height:
            return False, f"图片超出画布下边界: {y + height} > {self.bin_height}"

        # 2. 重叠检测
        for placed_img in self.placed_images:
            if self._check_overlap(x, y, width, height, placed_img):
                return False, f"与已放置图片重叠: {placed_img.get('name', 'Unknown')}"

        return True, "位置有效"

    def _check_overlap(self, x1: int, y1: int, w1: int, h1: int, placed_img: Dict) -> bool:
        """
        检查两个矩形是否重叠（新增的关键修复）

        Args:
            x1, y1, w1, h1: 新图片的位置和尺寸
            placed_img: 已放置的图片信息

        Returns:
            bool: 是否重叠
        """
        x2, y2, w2, h2 = placed_img['x'], placed_img['y'], placed_img['width'], placed_img['height']

        # 矩形重叠检测：如果两个矩形不重叠，则它们在某个方向上完全分离
        return not (x1 + w1 <= x2 or x2 + w2 <= x1 or y1 + h1 <= y2 or y2 + h2 <= y1)

    def _preprocess_image_dimensions(self, width: int, height: int):
        """
        预处理图片尺寸，处理间距和旋转逻辑

        Args:
            width: 原始图片宽度
            height: 原始图片高度

        Returns:
            tuple | None: (width_with_spacing, height_with_spacing, was_rotated) 或 None表示无法放置
        """
        # 考虑图片间距
        width_with_spacing = width + self.image_spacing
        height_with_spacing = height + self.image_spacing

        # 检查是否可以不旋转放置
        if width_with_spacing <= self.bin_width and (self.bin_height == 0 or height_with_spacing <= self.bin_height):
            return width_with_spacing, height_with_spacing, False

        # 尝试旋转
        if self.rotation_enabled:
            if height_with_spacing <= self.bin_width and (self.bin_height == 0 or width_with_spacing <= self.bin_height):
                return height_with_spacing, width_with_spacing, True

        # 无法放置 - 添加详细的日志信息（仅在详细模式下）
        if self.log_signal and self.enable_detailed_logging:
            if width_with_spacing > self.bin_width and height_with_spacing > self.bin_width:
                self.log_signal.emit(f"图片尺寸过大: {width}x{height}px (含间距: {width_with_spacing}x{height_with_spacing}px) 超过画布宽度 {self.bin_width}px，即使旋转也无法放置")
            elif width_with_spacing > self.bin_width:
                self.log_signal.emit(f"图片宽度过大: {width}px (含间距: {width_with_spacing}px) 超过画布宽度 {self.bin_width}px")
            elif not self.rotation_enabled:
                self.log_signal.emit(f"图片尺寸 {width}x{height}px 无法放置，旋转功能已禁用")
            else:
                self.log_signal.emit(f"图片尺寸 {width}x{height}px 无法放置在画布 {self.bin_width}x{self.bin_height}px 中")
        return None

    def _can_fit_without_rotation(self, width_with_spacing: int, height_with_spacing: int) -> bool:
        """
        检查图片是否可以不旋转放置

        Args:
            width_with_spacing: 包含间距的宽度
            height_with_spacing: 包含间距的高度

        Returns:
            bool: 是否可以放置
        """
        return (width_with_spacing <= self.bin_width and
                (self.bin_height == 0 or height_with_spacing <= self.bin_height))

    def _can_fit_with_rotation(self, width_with_spacing: int, height_with_spacing: int) -> bool:
        """
        检查图片是否可以旋转后放置

        Args:
            width_with_spacing: 包含间距的宽度
            height_with_spacing: 包含间距的高度

        Returns:
            bool: 是否可以旋转后放置
        """
        return (height_with_spacing <= self.bin_width and
                (self.bin_height == 0 or width_with_spacing <= self.bin_height))

    def _attempt_image_placement(self, width_with_spacing: int, height_with_spacing: int):
        """
        尝试放置图片，使用RectPack算法

        Args:
            width_with_spacing: 包含间距的宽度
            height_with_spacing: 包含间距的高度

        Returns:
            tuple | None: (x, y, actual_width, actual_height, test_packer) 或 None表示失败
        """
        # 生成唯一的矩形ID
        rect_id = self.placement_count + 1

        # 创建测试packer
        test_packer = self._create_test_packer()

        # 添加已有的矩形
        self._add_existing_rects_to_packer(test_packer)

        # 添加新矩形
        test_packer.add_rect(width_with_spacing, height_with_spacing, rid=rect_id)

        # 执行装箱
        test_packer.pack()

        # 查找放置结果
        return self._find_placement_result(test_packer, rect_id)

    def _create_test_packer(self):
        """
        创建测试用的packer实例

        Returns:
            packer: 新的packer实例
        """
        # 使用与主算法相同的优化配置
        from rectpack import MaxRectsBssf

        test_packer = newPacker(
            mode=1,  # Offline mode
            bin_algo=2,  # 使用默认的bin选择算法
            pack_algo=MaxRectsBssf,  # 使用相同的优化算法
            sort_algo=lambda rect_list: sorted(rect_list, key=lambda x: x[0] * x[1], reverse=True),
            rotation=False  # 我们已经手动处理旋转
        )
        test_packer.add_bin(self.bin_width, self.bin_height)
        return test_packer

    def _add_existing_rects_to_packer(self, test_packer):
        """
        将已有的矩形添加到packer中

        Args:
            test_packer: 目标packer实例
        """
        for i, img in enumerate(self.placed_images):
            test_packer.add_rect(
                img['width'] + self.image_spacing,
                img['height'] + self.image_spacing,
                rid=i + 1
            )

    def _find_placement_result(self, test_packer, rect_id):
        """
        从装箱结果中查找指定矩形的放置位置

        Args:
            test_packer: 装箱器实例
            rect_id: 矩形ID

        Returns:
            tuple | None: (x, y, actual_width, actual_height, test_packer) 或 None表示失败
        """
        placed_rects = test_packer.rect_list()

        # rectpack库返回的是tuple格式: (bin_id, x, y, width, height, rid)
        for rect in placed_rects:
            bin_id, x, y, width, height, rid = rect
            if rid == rect_id:
                return x, y, width, height, test_packer

        # 未找到放置的矩形
        return None

    def _update_placement_state(self, x: int, y: int, actual_width: int, actual_height: int,
                               was_rotated: bool, image_data: Dict[str, Any], test_packer):
        """
        更新放置状态和记录

        Args:
            x: x坐标
            y: y坐标
            actual_width: 实际宽度
            actual_height: 实际高度
            was_rotated: 是否旋转
            image_data: 图片数据
            test_packer: 测试packer实例
        """
        # 更新图片数据中的旋转信息
        if image_data and was_rotated:
            image_data['need_rotation'] = True
            if self.log_signal and self.enable_detailed_logging:
                self.log_signal.emit(f"图片已旋转: {image_data.get('name', 'Unknown')}")

        # 更新统计信息
        self.placement_count += 1
        self.used_area += actual_width * actual_height
        self.current_max_height = max(self.current_max_height, y + actual_height)

        # 创建放置的图片记录
        placed_image = {
            'x': x,
            'y': y,
            'width': actual_width - self.image_spacing,
            'height': actual_height - self.image_spacing,
            'rotated': was_rotated,
            'rect_id': self.placement_count
        }

        if image_data:
            placed_image.update(image_data)

        self.placed_images.append(placed_image)

        # 更新主packer
        self.packer = test_packer

    def find_position(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        为图片找到合适的位置（兼容现有API）

        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        return self.place_image(width, height, image_data)

    def create_image_identity(self, image_info: Dict[str, Any]) -> Optional[ImageIdentity]:
        """
        为图片创建唯一身份信息

        Args:
            image_info: 图片信息字典

        Returns:
            ImageIdentity: 图片身份信息，如果生成器不可用返回None
        """
        if not self.id_generator:
            return None

        try:
            identity = self.id_generator.create_image_identity(image_info)
            if self.log_signal:
                self.log_signal.emit(f"🆔 生成唯一身份: {identity.unique_id} -> {identity.layer_name}")
            return identity
        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"❌ 生成唯一身份失败: {str(e)}")
            return None

    def _get_current_time_string(self, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """
        获取当前时间字符串，使用统一的时间处理工具

        Args:
            format_str: 时间格式字符串

        Returns:
            str: 格式化的时间字符串
        """
        try:
            from utils.time_helper import get_current_time_string
            return get_current_time_string(format_str)
        except ImportError:
            # 如果时间工具不可用，使用备用方法
            try:
                import time
                return time.strftime(format_str)
            except Exception:
                return 'Unknown Time'

    def get_image_identity_statistics(self) -> Dict[str, Any]:
        """
        获取图片身份统计信息

        Returns:
            Dict: 统计信息
        """
        if not self.id_generator:
            return {'available': False, 'message': '唯一ID生成器不可用'}

        try:
            stats = self.id_generator.get_statistics()
            stats['available'] = True
            return stats
        except Exception as e:
            return {'available': False, 'error': str(e)}

    def place_image_with_unified_interface(self, image_info: Dict[str, Any], mode: str = 'production') -> Tuple[int, int, bool]:
        """
        使用统一接口放置图片

        Args:
            image_info: 图片信息字典，包含 name, path, width, height 等
            mode: 处理模式 ('test' 或 'production')

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        if not UNIFIED_PROCESSOR_AVAILABLE or not self.unified_processor:
            # 回退到传统方法
            return self.place_image(
                image_info.get('width', 0),
                image_info.get('height', 0),
                image_info
            )

        try:
            # 步骤1: 使用传统算法计算位置
            x, y, success = self.place_image(
                image_info.get('width', 0),
                image_info.get('height', 0),
                image_info
            )

            if not success:
                return x, y, success

            # 步骤2: 创建统一数据结构
            position_result = {
                'x': x,
                'y': y,
                'width': image_info.get('width', 0),
                'height': image_info.get('height', 0),
                'rotated': image_info.get('need_rotation', False)
            }

            layer_info = {
                'index': self.placement_count - 1,  # 已经递增了
                'total': len(self.placed_images) + 1
            }

            # 步骤3: 创建统一放置数据
            placement_data = self.unified_processor.create_placement_data(
                image_info, position_result, layer_info
            )

            # 步骤4: 使用统一接口处理
            unified_success = self.unified_processor.process_image(placement_data, mode)

            if unified_success:
                if self.log_signal:
                    self.log_signal.emit(f"✅ 统一接口处理成功: {placement_data.layer_name}")
            else:
                if self.log_signal:
                    self.log_signal.emit(f"⚠️ 统一接口处理失败: {placement_data.layer_name}")
                    for error in placement_data.validation_errors:
                        self.log_signal.emit(f"  错误: {error}")

            return x, y, success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"❌ 统一接口处理异常: {str(e)}")
            # 回退到传统方法
            return self.place_image(
                image_info.get('width', 0),
                image_info.get('height', 0),
                image_info
            )

    def _simple_place_image(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        简化的图片放置算法 - 主入口，分解为更小模块，支持旋转

        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 第一步：预处理尺寸，包括旋转检查
        processed_result = self._preprocess_simple_dimensions(width, height, image_data)
        if processed_result is None:
            return 0, 0, False

        final_width, final_height, was_rotated = processed_result
        width_with_spacing, height_with_spacing = self._calculate_simple_dimensions(final_width, final_height)

        # 第二步：找到放置位置
        x, y = self._find_simple_placement_position(width_with_spacing, height_with_spacing)

        # 第三步：检查高度限制
        if not self._check_simple_height_constraint(y, height_with_spacing):
            self.canvas_is_full = True
            return 0, 0, False

        # 第四步：更新状态
        self._update_simple_placement_state(x, y, final_width, final_height, width_with_spacing, height_with_spacing, image_data, was_rotated)

        return x, y, True

    def _preprocess_simple_dimensions(self, width: int, height: int, image_data: Dict[str, Any] = None):
        """
        简化算法的图片尺寸预处理，支持旋转

        Args:
            width: 原始宽度
            height: 原始高度
            image_data: 图片数据

        Returns:
            tuple | None: (final_width, final_height, was_rotated) 或 None表示无法放置
        """
        # 考虑间距
        width_with_spacing = width + self.image_spacing
        height_with_spacing = height + self.image_spacing

        # 检查是否可以直接放置
        if width_with_spacing <= self.bin_width:
            return width, height, False

        # 尝试旋转
        if self.rotation_enabled and height_with_spacing <= self.bin_width:
            if self.log_signal:
                name = image_data.get('name', 'Unknown') if image_data else 'Unknown'
                self.log_signal.emit(f"简化算法：图片 {name} 旋转90度以适应画布 (原始: {width}x{height}px -> 旋转后: {height}x{width}px)")
            # 更新图片数据中的旋转状态
            if image_data:
                image_data['need_rotation'] = True
            return height, width, True

        # 无法放置
        if self.log_signal:
            name = image_data.get('name', 'Unknown') if image_data else 'Unknown'
            if width_with_spacing > self.bin_width and height_with_spacing > self.bin_width:
                self.log_signal.emit(f"简化算法: 图片 {name} 尺寸过大 ({width}x{height}px) 即使旋转也无法放置在画布宽度 {self.bin_width}px 中")
            elif not self.rotation_enabled:
                self.log_signal.emit(f"简化算法: 图片 {name} 宽度 {width_with_spacing}px 超过画布宽度 {self.bin_width}px，旋转功能已禁用")
            else:
                self.log_signal.emit(f"简化算法: 图片 {name} 宽度 {width_with_spacing}px 超过画布宽度 {self.bin_width}px")
        return None

    def _calculate_simple_dimensions(self, width: int, height: int) -> Tuple[int, int]:
        """
        计算包含间距的尺寸

        Args:
            width: 原始宽度
            height: 原始高度

        Returns:
            Tuple[int, int]: (width_with_spacing, height_with_spacing)
        """
        return width + self.image_spacing, height + self.image_spacing

    def _find_simple_placement_position(self, width_with_spacing: int, height_with_spacing: int) -> Tuple[int, int]:
        """
        找到简单放置的位置

        Args:
            width_with_spacing: 包含间距的宽度
            height_with_spacing: 包含间距的高度

        Returns:
            Tuple[int, int]: (x, y)
        """
        if not self.placed_images:
            return 0, 0

        # 获取当前行信息
        row_info = self._get_current_row_info()

        # 尝试在当前行放置
        if row_info['right'] + width_with_spacing <= self.bin_width:
            return row_info['right'], row_info['y']
        else:
            # 创建新行
            return 0, row_info['y'] + row_info['height']

    def _get_current_row_info(self) -> Dict[str, int]:
        """
        获取当前行的信息

        Returns:
            Dict[str, int]: 包含 y, right, height 的字典
        """
        current_row_y = 0
        current_row_right = 0
        current_row_height = 0

        for img in self.placed_images:
            if img['y'] == current_row_y:
                current_row_right = max(current_row_right, img['x'] + img['width'] + self.image_spacing)
                current_row_height = max(current_row_height, img['height'] + self.image_spacing)
            else:
                if img['y'] > current_row_y:
                    current_row_y = img['y']
                    current_row_right = img['x'] + img['width'] + self.image_spacing
                    current_row_height = img['height'] + self.image_spacing

        return {
            'y': current_row_y,
            'right': current_row_right,
            'height': current_row_height
        }

    def _check_simple_height_constraint(self, y: int, height_with_spacing: int) -> bool:
        """
        检查高度约束

        Args:
            y: y坐标
            height_with_spacing: 包含间距的高度

        Returns:
            bool: 是否符合高度约束
        """
        if self.bin_height > 0 and y + height_with_spacing > self.bin_height:
            return False
        return True

    def _update_simple_placement_state(self, x: int, y: int, width: int, height: int,
                                      width_with_spacing: int, height_with_spacing: int,
                                      image_data: Dict[str, Any], was_rotated: bool = False):
        """
        更新简单放置的状态

        Args:
            x: x坐标
            y: y坐标
            width: 原始宽度
            height: 原始高度
            width_with_spacing: 包含间距的宽度
            height_with_spacing: 包含间距的高度
            image_data: 图片数据
            was_rotated: 是否旋转
        """
        # 更新统计信息
        self.placement_count += 1
        self.used_area += width_with_spacing * height_with_spacing
        self.current_max_height = max(self.current_max_height, y + height_with_spacing)

        # 创建放置的图片记录
        placed_image = {
            'x': x,
            'y': y,
            'width': width,
            'height': height,
            'rotated': was_rotated,
            'rect_id': self.placement_count
        }

        if image_data:
            placed_image.update(image_data)

        self.placed_images.append(placed_image)

    def get_layout_info(self) -> Dict[str, Any]:
        """
        获取布局信息

        优化画布高度计算：
        1. PS的画布最大高度，不超过config最大高度，且尽可能接近最大高度
        2. 当所有图片排列完成时，以最底部图片的底边为画布高度
        3. 确保画布高度不超过配置的最大高度限制

        Returns:
            Dict[str, Any]: 布局信息
        """
        # 计算优化后的画布高度
        optimized_height = self._calculate_optimized_canvas_height()

        total_container_area = self.bin_width * optimized_height
        utilization = (self.used_area / total_container_area * 100) if total_container_area > 0 else 0

        return {
            'container_width': self.bin_width,
            'container_height': optimized_height,
            'total_area': total_container_area,
            'used_area': self.used_area,
            'utilization_percent': utilization,
            'placed_count': self.placement_count,
            'canvas_is_full': self.canvas_is_full,
            'max_height_limit': self.bin_height if self.bin_height != 999999999 else None
        }

    def _calculate_optimized_canvas_height(self) -> int:
        """
        计算优化后的画布高度

        优化策略：
        1. 以最底部图片的底边为基准高度
        2. 确保不超过配置的最大高度限制
        3. 尽可能接近最大高度，提高空间利用率

        Returns:
            int: 优化后的画布高度
        """
        if not self.placed_images:
            # 如果没有图片，返回最小高度
            return 100

        # 计算最底部图片的底边位置
        max_bottom = 0
        for img in self.placed_images:
            if 'y' in img and 'height' in img:
                bottom = img['y'] + img['height']
                max_bottom = max(max_bottom, bottom)

        # 如果有最大高度限制
        if self.bin_height != 999999999:
            # 确保不超过最大高度限制
            optimized_height = min(max_bottom, self.bin_height)

            # 如果实际使用高度远小于最大高度，使用实际高度
            # 如果实际使用高度接近最大高度，使用最大高度以提高利用率
            height_ratio = max_bottom / self.bin_height

            if height_ratio >= 0.95:  # 如果使用了95%以上的高度
                # 使用最大高度，提高利用率
                optimized_height = self.bin_height
                if self.log_signal:
                    self.log_signal.emit(f"📏 画布高度优化: 使用最大高度 {optimized_height}px (利用率 {height_ratio*100:.1f}%)")
            else:
                # 使用实际高度，避免浪费
                optimized_height = max_bottom
                if self.log_signal:
                    self.log_signal.emit(f"📏 画布高度优化: 使用实际高度 {optimized_height}px (最大高度 {self.bin_height}px)")
        else:
            # 没有高度限制，使用实际高度
            optimized_height = max_bottom

        # 确保高度至少为100像素
        optimized_height = max(optimized_height, 100)

        # 更新current_max_height
        self.current_max_height = optimized_height

        return optimized_height

    def reset(self, silent=False):
        """重置排列器状态

        Args:
            silent: 是否静默重置（不输出日志）
        """
        self.placed_images.clear()
        self.placement_count = 0
        self.used_area = 0
        self.current_max_height = 0
        self.canvas_is_full = False
        self._initialize_packer(silent=silent)

        if self.log_signal and not silent:
            self.log_signal.emit("RectPack排列器已重置")

    def set_algorithm_params(self, rotation_enabled: bool = True, sort_key: int = SORT_AREA,
                           pack_algo: int = BinPack.BNF):
        """
        设置算法参数

        Args:
            rotation_enabled: 是否启用旋转
            sort_key: 排序策略
            pack_algo: 装箱算法
        """
        self.rotation_enabled = rotation_enabled
        self.sort_key = sort_key
        self.pack_algo = pack_algo

        # 重新初始化packer（静默模式）
        self._initialize_packer(silent=True)

        if self.log_signal:
            self.log_signal.emit(f"更新RectPack参数: 旋转={'启用' if rotation_enabled else '禁用'}, 排序策略={sort_key}")

    def _set_algorithm_params_silent(self, rotation_enabled: bool = True, sort_key: int = SORT_AREA,
                                   pack_algo: int = BinPack.BNF):
        """
        静默设置算法参数（不输出日志）

        Args:
            rotation_enabled: 是否启用旋转
            sort_key: 排序策略
            pack_algo: 装箱算法
        """
        self.rotation_enabled = rotation_enabled
        self.sort_key = sort_key
        self.pack_algo = pack_algo

        # 重新初始化packer（静默模式）
        self._initialize_packer(silent=True)

    def optimize_for_utilization(self) -> bool:
        """
        优化画布利用率 - 主入口，分解为更小模块

        Returns:
            bool: 是否成功优化
        """
        if not self.placed_images:
            return False

        # 检查是否启用优化
        if not getattr(self, 'enable_optimization', True):
            if self.log_signal:
                self.log_signal.emit("优化功能已禁用")
            return False

        # 第一步：保存当前状态
        original_state = self._save_current_state()

        # 第二步：寻找最佳配置
        best_config, best_utilization = self._find_best_configuration(original_state)

        # 第三步：应用最佳配置或恢复原始状态
        return self._apply_best_configuration(best_config, best_utilization, original_state)

    def _save_current_state(self):
        """
        保存当前状态

        Returns:
            dict: 包含原始图片和统计信息的状态
        """
        return {
            'images': self.placed_images.copy(),
            'stats': self.get_layout_info()
        }

    def _find_best_configuration(self, original_state: Dict[str, Any]) -> Tuple[Tuple, float]:
        """
        寻找最佳配置

        Args:
            original_state: 原始状态

        Returns:
            Tuple[Tuple, float]: (最佳配置, 最佳利用率)
        """
        best_utilization = original_state['stats']['utilization_percent']
        best_config = None

        # 获取所有可能的配置组合
        configurations = self._get_all_configurations()

        for config in configurations:
            utilization = self._test_configuration(config, original_state['images'])
            if utilization > best_utilization:
                best_utilization = utilization
                best_config = config

        return best_config, best_utilization

    def _get_all_configurations(self):
        """
        获取所有可能的配置组合，基于当前参数设置生成优化组合

        Returns:
            list: 配置组合列表
        """
        # 基于当前设置生成优化组合
        configurations = []

        # 如果启用了旋转，尝试两种旋转设置
        rotation_options = [self.rotation_enabled]
        if self.rotation_enabled:
            rotation_options.append(False)  # 也尝试不旋转

        # 尝试不同的排序策略
        sort_strategies = [self.sort_key]  # 当前设置
        # 添加其他可能的排序策略
        other_sorts = [SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO]
        for sort_key in other_sorts:
            if sort_key != self.sort_key:
                sort_strategies.append(sort_key)

        # 尝试不同的装箱算法
        pack_algorithms = [self.pack_algo]  # 当前设置
        other_packs = [BinPack.BNF, BinPack.BFF, BinPack.BBF]
        for pack_algo in other_packs:
            if pack_algo != self.pack_algo:
                pack_algorithms.append(pack_algo)

        # 生成组合，优先使用当前设置的变体
        max_configs = getattr(self, 'optimization_iterations', 5) * 2  # 限制组合数量

        for rotation in rotation_options:
            for sort_key in sort_strategies[:3]:  # 只尝试前3种排序策略
                for pack_algo in pack_algorithms[:2]:  # 只尝试前2种装箱算法
                    configurations.append((rotation, sort_key, pack_algo))
                    if len(configurations) >= max_configs:
                        return configurations

        return configurations

    def _test_configuration(self, config, original_images):
        """
        测试特定配置的效果，应用旋转惩罚和宽高比偏好

        Args:
            config: 配置参数 (rotation, sort_key, pack_algo)
            original_images: 原始图片列表

        Returns:
            float: 调整后的利用率
        """
        rotation, sort_key, pack_algo = config

        # 重置并设置新配置（静默模式）
        self.reset(silent=True)
        self._set_algorithm_params_silent(rotation, sort_key, pack_algo)

        # 尝试放置所有图片
        success = self._place_all_images(original_images)

        if success:
            stats = self.get_layout_info()
            base_utilization = stats['utilization_percent']

            # 应用优化参数调整
            adjusted_utilization = self._apply_optimization_adjustments(
                base_utilization, rotation, original_images
            )

            return adjusted_utilization
        else:
            return 0.0  # 如果放置失败，返回0利用率

    def _apply_optimization_adjustments(self, base_utilization: float, rotation_enabled: bool, images: List[Dict]) -> float:
        """
        应用优化参数调整，包括旋转惩罚和宽高比偏好

        Args:
            base_utilization: 基础利用率
            rotation_enabled: 是否启用旋转
            images: 图片列表

        Returns:
            float: 调整后的利用率
        """
        adjusted_utilization = base_utilization

        # 应用旋转惩罚
        if rotation_enabled:
            rotation_penalty = getattr(self, 'rotation_penalty', 0.05)
            rotated_count = sum(1 for img in self.placed_images if img.get('rotated', False))
            total_count = len(self.placed_images)

            if total_count > 0:
                rotation_ratio = rotated_count / total_count
                penalty = rotation_ratio * rotation_penalty * 100  # 转换为百分比
                adjusted_utilization -= penalty

        # 应用宽高比偏好
        aspect_ratio_preference = getattr(self, 'aspect_ratio_preference', 1.0)
        if aspect_ratio_preference != 1.0:
            # 计算平均宽高比
            total_aspect_ratio = 0
            for img in self.placed_images:
                width = img.get('width', 1)
                height = img.get('height', 1)
                aspect_ratio = width / height if height > 0 else 1.0
                total_aspect_ratio += aspect_ratio

            if len(self.placed_images) > 0:
                avg_aspect_ratio = total_aspect_ratio / len(self.placed_images)
                # 如果平均宽高比接近偏好值，给予奖励
                ratio_diff = abs(avg_aspect_ratio - aspect_ratio_preference)
                if ratio_diff < 0.5:
                    bonus = (0.5 - ratio_diff) * 2  # 最大奖励1%
                    adjusted_utilization += bonus

        return max(0.0, adjusted_utilization)  # 确保不为负数

    def _place_all_images(self, images):
        """
        尝试放置所有图片

        Args:
            images: 图片列表

        Returns:
            bool: 是否成功放置所有图片
        """
        for img in images:
            _, _, placed = self.place_image(
                img.get('width', 0),
                img.get('height', 0),
                img
            )
            if not placed:
                return False
        return True

    def _apply_best_configuration(self, best_config, best_utilization, original_state):
        """
        应用最佳配置或恢复原始状态

        Args:
            best_config: 最佳配置
            best_utilization: 最佳利用率
            original_state: 原始状态

        Returns:
            bool: 是否成功优化
        """
        if best_config:
            # 应用最佳配置
            self.reset(silent=True)
            self._set_algorithm_params_silent(*best_config)

            # 重新放置所有图片
            self._place_all_images(original_state['images'])

            # 记录优化结果
            if self.log_signal:
                original_utilization = original_state['stats']['utilization_percent']
                self.log_signal.emit(f"优化完成: 利用率从 {original_utilization:.2f}% 提升到 {best_utilization:.2f}%")

            return True
        else:
            # 恢复原始状态
            self.placed_images = original_state['images']
            return False

    # ==================== 俄罗斯方块式紧密排列算法 ====================

    def tetris_place_image(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        兼容性方法：使用RectPack算法放置图片

        注意：此方法名保留"tetris"是为了API兼容性，但实际使用的是RectPack算法
        已完全替换原有的tetris算法实现

        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        if self.canvas_is_full:
            return 0, 0, False

        # 第一步：预处理图片尺寸
        processed_result = self._tetris_preprocess_dimensions(width, height, image_data)
        if processed_result is None:
            return 0, 0, False

        final_width, final_height, was_rotated = processed_result

        # 第二步：使用天际线算法寻找最佳位置
        position_result = self._tetris_find_best_position(final_width, final_height, image_data)
        if position_result is None:
            self.canvas_is_full = True
            if self.log_signal:
                self.log_signal.emit(f"俄罗斯方块算法：画布已满，无法放置图片 {final_width}x{final_height}px")
            return 0, 0, False

        x, y = position_result

        # 第三步：更新状态和记录
        self._tetris_update_placement_state(x, y, final_width, final_height, was_rotated, image_data)

        return x, y, True

    def _tetris_preprocess_dimensions(self, width: int, height: int, image_data: Dict[str, Any] = None):
        """
        俄罗斯方块算法的图片尺寸预处理

        Args:
            width: 原始宽度
            height: 原始高度
            image_data: 图片数据

        Returns:
            tuple | None: (final_width, final_height, was_rotated) 或 None
        """
        # 考虑间距
        width_with_spacing = width + self.image_spacing
        height_with_spacing = height + self.image_spacing

        # 检查是否可以直接放置
        if self._tetris_can_fit(width_with_spacing, height_with_spacing):
            return width_with_spacing, height_with_spacing, False

        # 尝试旋转
        if self.rotation_enabled and self._tetris_can_fit(height_with_spacing, width_with_spacing):
            if self.log_signal:
                name = image_data.get('name', 'Unknown') if image_data else 'Unknown'
                self.log_signal.emit(f"俄罗斯方块算法：图片 {name} 旋转90度以适应画布")
            return height_with_spacing, width_with_spacing, True

        # 无法放置
        if self.log_signal:
            name = image_data.get('name', 'Unknown') if image_data else 'Unknown'
            self.log_signal.emit(f"俄罗斯方块算法：图片 {name} ({width}x{height}px) 无法放置")
        return None

    def _tetris_can_fit(self, width: int, height: int) -> bool:
        """
        检查图片是否可以放置在画布中

        Args:
            width: 宽度（包含间距）
            height: 高度（包含间距）

        Returns:
            bool: 是否可以放置
        """
        return (width <= self.bin_width and
                (self.bin_height == 0 or height <= self.bin_height))

    def _tetris_find_best_position(self, width: int, height: int, image_data: Dict[str, Any] = None):
        """
        使用天际线算法寻找最佳放置位置

        天际线算法核心思想：
        1. 维护一个天际线，记录每个x坐标的最高点
        2. 寻找能够放置图片的最低位置
        3. 优先选择能够减少间隙的位置
        4. 实现类似俄罗斯方块的紧密堆叠

        Args:
            width: 图片宽度（包含间距）
            height: 图片高度（包含间距）
            image_data: 图片数据

        Returns:
            tuple | None: (x, y) 或 None表示无法放置
        """
        # 如果是第一个图片，放在左上角
        if not self.placed_images:
            return 0, 0

        # 创建天际线
        skyline = self._tetris_create_skyline()

        # 寻找所有可能的放置位置
        candidates = self._tetris_find_candidate_positions(width, height, skyline)

        if not candidates:
            return None

        # 选择最佳位置
        best_position = self._tetris_select_best_position(candidates, width, height, image_data)

        return best_position

    def _tetris_create_skyline(self) -> List[int]:
        """
        创建天际线数组，记录每个x坐标的最高点

        Returns:
            List[int]: 天际线数组，索引为x坐标，值为该位置的最高y坐标
        """
        skyline = [0] * (self.bin_width + 1)

        for img in self.placed_images:
            x_start = img['x']
            x_end = img['x'] + img['width'] + self.image_spacing
            y_bottom = img['y'] + img['height'] + self.image_spacing

            # 更新天际线
            for x in range(x_start, min(x_end, self.bin_width)):
                skyline[x] = max(skyline[x], y_bottom)

        return skyline

    def _tetris_find_candidate_positions(self, width: int, height: int, skyline: List[int]) -> List[Tuple[int, int]]:
        """
        寻找所有可能的候选位置

        Args:
            width: 图片宽度
            height: 图片高度
            skyline: 天际线数组

        Returns:
            List[Tuple[int, int]]: 候选位置列表 [(x, y), ...]
        """
        candidates = []

        # 遍历所有可能的x位置
        for x in range(0, self.bin_width - width + 1):
            # 找到该位置的最低可放置y坐标
            y = 0
            for i in range(x, x + width):
                y = max(y, skyline[i])

            # 检查高度限制
            if self.bin_height > 0 and y + height > self.bin_height:
                continue

            # 检查是否与现有图片重叠
            if not self._tetris_check_collision(x, y, width, height):
                candidates.append((x, y))

        return candidates

    def _tetris_check_collision(self, x: int, y: int, width: int, height: int) -> bool:
        """
        检查指定位置是否与现有图片发生碰撞

        Args:
            x: x坐标
            y: y坐标
            width: 宽度
            height: 高度

        Returns:
            bool: True表示发生碰撞，False表示无碰撞
        """
        for img in self.placed_images:
            img_x = img['x']
            img_y = img['y']
            img_width = img['width'] + self.image_spacing
            img_height = img['height'] + self.image_spacing

            # 检查矩形重叠
            if not (x + width <= img_x or x >= img_x + img_width or
                    y + height <= img_y or y >= img_y + img_height):
                return True

        return False

    def _tetris_select_best_position(self, candidates: List[Tuple[int, int]], width: int, height: int,
                                   image_data: Dict[str, Any] = None) -> Tuple[int, int]:
        """
        从候选位置中选择最佳位置

        评分标准：
        1. 优先选择y坐标较低的位置（底部优先）
        2. 在相同y坐标下，优先选择能够减少间隙的位置
        3. 优先选择能够与现有图片"粘合"的位置
        4. 优先选择x坐标较小的位置（左对齐）

        Args:
            candidates: 候选位置列表
            width: 图片宽度
            height: 图片高度
            image_data: 图片数据

        Returns:
            Tuple[int, int]: 最佳位置 (x, y)
        """
        if not candidates:
            return None

        # 按评分排序
        scored_candidates = []
        for x, y in candidates:
            score = self._tetris_calculate_position_score(x, y, width, height)
            scored_candidates.append((score, x, y))

        # 选择评分最高的位置
        scored_candidates.sort(key=lambda item: (-item[0], item[2], item[1]))  # 评分降序，y升序，x升序

        _, best_x, best_y = scored_candidates[0]
        return best_x, best_y

    def _tetris_calculate_position_score(self, x: int, y: int, width: int, height: int) -> float:
        """
        计算位置评分

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度
            height: 图片高度

        Returns:
            float: 位置评分，越高越好
        """
        score = 0.0

        # 1. 底部优先：y坐标越小评分越高
        score += (self.bin_height - y) * 1000 if self.bin_height > 0 else (10000 - y)

        # 2. 左对齐优先：x坐标越小评分越高
        score += (self.bin_width - x) * 10

        # 3. 粘合度评分：与现有图片的接触面积
        contact_score = self._tetris_calculate_contact_score(x, y, width, height)
        score += contact_score * 100

        # 4. 间隙减少评分：能够填充现有间隙的程度
        gap_fill_score = self._tetris_calculate_gap_fill_score(x, y, width, height)
        score += gap_fill_score * 50

        return score

    def _tetris_calculate_contact_score(self, x: int, y: int, width: int, height: int) -> float:
        """
        计算与现有图片的接触评分

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度
            height: 图片高度

        Returns:
            float: 接触评分
        """
        contact_score = 0.0

        for img in self.placed_images:
            img_x = img['x']
            img_y = img['y']
            img_width = img['width'] + self.image_spacing
            img_height = img['height'] + self.image_spacing

            # 检查底部接触（新图片放在现有图片上方）
            if (y == img_y + img_height and
                not (x + width <= img_x or x >= img_x + img_width)):
                overlap_width = min(x + width, img_x + img_width) - max(x, img_x)
                contact_score += overlap_width / width

            # 检查左侧接触
            if (x == img_x + img_width and
                not (y + height <= img_y or y >= img_y + img_height)):
                overlap_height = min(y + height, img_y + img_height) - max(y, img_y)
                contact_score += overlap_height / height

        return contact_score

    def _tetris_calculate_gap_fill_score(self, x: int, y: int, width: int, height: int) -> float:
        """
        计算间隙填充评分

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度
            height: 图片高度

        Returns:
            float: 间隙填充评分
        """
        # 检查是否能够完美填充水平空间
        if x == 0 and x + width == self.bin_width:
            return 1.0  # 完美填充整行

        # 检查是否能够与现有图片组合填充整行
        row_fill_score = self._tetris_calculate_row_fill_score(x, y, width, height)

        return row_fill_score

    def _tetris_calculate_row_fill_score(self, x: int, y: int, width: int, height: int) -> float:
        """
        计算行填充评分

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度
            height: 图片高度

        Returns:
            float: 行填充评分
        """
        # 找到同一行的图片
        row_images = []
        tolerance = max(5, height * 0.1)  # 允许一定的y坐标误差

        for img in self.placed_images:
            if abs(img['y'] - y) <= tolerance:
                row_images.append(img)

        if not row_images:
            return 0.0

        # 计算行的总宽度
        total_width = width
        for img in row_images:
            total_width += img['width'] + self.image_spacing

        # 计算填充率
        fill_ratio = total_width / self.bin_width

        # 如果接近完美填充，给予高分
        if fill_ratio >= 0.95:
            return 1.0
        elif fill_ratio >= 0.85:
            return 0.7
        elif fill_ratio >= 0.75:
            return 0.5
        else:
            return 0.2

    def _tetris_update_placement_state(self, x: int, y: int, width: int, height: int,
                                     was_rotated: bool, image_data: Dict[str, Any]):
        """
        更新俄罗斯方块算法的放置状态

        Args:
            x: x坐标
            y: y坐标
            width: 宽度（包含间距）
            height: 高度（包含间距）
            was_rotated: 是否旋转
            image_data: 图片数据
        """
        # 更新图片数据中的旋转信息
        if image_data and was_rotated:
            image_data['need_rotation'] = True

        # 更新统计信息
        self.placement_count += 1
        actual_width = width - self.image_spacing
        actual_height = height - self.image_spacing
        self.used_area += actual_width * actual_height
        self.current_max_height = max(self.current_max_height, y + height)

        # 创建放置的图片记录
        placed_image = {
            'x': x,
            'y': y,
            'width': actual_width,
            'height': actual_height,
            'rotated': was_rotated,
            'rect_id': self.placement_count,
            'algorithm': 'tetris'  # 标记使用的算法
        }

        if image_data:
            placed_image.update(image_data)

        self.placed_images.append(placed_image)

        # 记录日志
        if self.log_signal:
            name = image_data.get('name', f'Image_{self.placement_count}') if image_data else f'Image_{self.placement_count}'
            rotation_info = " (旋转90度)" if was_rotated else ""
            self.log_signal.emit(f"俄罗斯方块算法：成功放置图片 {name} ({actual_width}x{actual_height}px) 在 ({x}, {y}){rotation_info}")

    # ==================== 测试模式支持 (参照Tetris算法逻辑) ====================

    def create_test_mode_canvas(self, canvas_width_px: int, canvas_height_px: int,
                               canvas_name: str, miniature_ratio: float = None) -> bool:
        """
        创建测试模式画布 - 使用统一的单位转换，兼容性接口

        Args:
            canvas_width_px: 画布宽度（像素）
            canvas_height_px: 画布高度（像素）
            canvas_name: 画布名称
            miniature_ratio: 缩小比率（已废弃，保留兼容性，忽略此参数）

        Returns:
            bool: 是否创建成功
        """
        # 忽略miniature_ratio参数，使用统一的单位转换方式
        if miniature_ratio is not None and self.log_signal:
            self.log_signal.emit("注意: miniature_ratio参数已废弃，使用统一的cm转px转换方式")

        return self.create_test_mode_canvas_with_pil(canvas_width_px, canvas_height_px, canvas_name)

    def create_test_mode_canvas_with_pil(self, canvas_width_px: int, canvas_height_px: int,
                                        canvas_name: str, miniature_ratio: float = None) -> bool:
        """
        使用PIL创建测试模式画布 - 使用统一的单位转换

        Args:
            canvas_width_px: 画布宽度（像素）
            canvas_height_px: 画布高度（像素）
            canvas_name: 画布名称
            miniature_ratio: 缩小比率（已废弃，忽略此参数）

        Returns:
            bool: 是否创建成功
        """
        try:
            # 忽略miniature_ratio参数，使用统一的单位转换方式
            if miniature_ratio is not None and self.log_signal:
                self.log_signal.emit("注意: miniature_ratio参数已废弃，使用统一的cm转px转换方式")

            # 第一步：验证参数
            if not self._validate_test_canvas_params_unified(canvas_width_px, canvas_height_px, canvas_name):
                return False

            # 第二步：使用统一的单位转换，直接使用传入的像素尺寸
            # 测试模式下，坐标和尺寸已经通过cm转px的方式处理
            scaled_width = canvas_width_px
            scaled_height = canvas_height_px

            # 第三步：创建PIL画布对象
            success = self._create_pil_canvas_objects_unified(scaled_width, scaled_height, canvas_name)

            if success and self.log_signal:
                self.log_signal.emit(f"测试模式PIL画布创建成功: {canvas_name} ({scaled_width}x{scaled_height}px) (统一单位转换)")

            return success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"创建测试模式PIL画布失败: {str(e)}")
            return False

    def _validate_test_canvas_params(self, canvas_width_px: int, canvas_height_px: int,
                                   canvas_name: str, miniature_ratio: float) -> bool:
        """
        验证测试画布参数

        Args:
            canvas_width_px: 画布宽度
            canvas_height_px: 画布高度
            canvas_name: 画布名称
            miniature_ratio: 缩小比率

        Returns:
            bool: 参数是否有效
        """
        if canvas_width_px <= 0 or canvas_height_px <= 0:
            if self.log_signal:
                self.log_signal.emit(f"无效的画布尺寸: {canvas_width_px}x{canvas_height_px}px")
            return False

        if not canvas_name or not canvas_name.strip():
            if self.log_signal:
                self.log_signal.emit("画布名称不能为空")
            return False

        if miniature_ratio <= 0 or miniature_ratio > 1:
            if self.log_signal:
                self.log_signal.emit(f"无效的缩小比率: {miniature_ratio}")
            return False

        return True

    def _validate_test_canvas_params_unified(self, canvas_width_px: int, canvas_height_px: int,
                                           canvas_name: str) -> bool:
        """
        验证测试画布参数 - 统一版本，移除miniature_ratio验证

        Args:
            canvas_width_px: 画布宽度
            canvas_height_px: 画布高度
            canvas_name: 画布名称

        Returns:
            bool: 参数是否有效
        """
        if canvas_width_px <= 0 or canvas_height_px <= 0:
            if self.log_signal:
                self.log_signal.emit(f"无效的画布尺寸: {canvas_width_px}x{canvas_height_px}px")
            return False

        if not canvas_name or not canvas_name.strip():
            if self.log_signal:
                self.log_signal.emit("画布名称不能为空")
            return False

        # 限制最大尺寸以避免内存问题
        max_test_size = 5000  # 提高最大尺寸限制
        if canvas_width_px > max_test_size or canvas_height_px > max_test_size:
            if self.log_signal:
                self.log_signal.emit(f"警告: 画布尺寸过大 ({canvas_width_px}x{canvas_height_px}px)，可能影响性能")

        return True

    def _create_pil_canvas_objects_unified(self, scaled_width: int, scaled_height: int,
                                         canvas_name: str) -> bool:
        """
        创建PIL画布对象 - 统一版本，移除miniature_ratio参数

        Args:
            scaled_width: 画布宽度
            scaled_height: 画布高度
            canvas_name: 画布名称

        Returns:
            bool: 是否创建成功
        """
        try:
            from PIL import Image, ImageDraw

            # 创建PIL图像对象
            self.test_pil_image = Image.new('RGB', (scaled_width, scaled_height), 'white')
            self.test_pil_draw = ImageDraw.Draw(self.test_pil_image)

            # 绘制边框
            self.test_pil_draw.rectangle(
                [0, 0, scaled_width-1, scaled_height-1],
                outline='black',
                width=2
            )

            # 保存画布信息（不再保存miniature_ratio）
            self.test_canvas_name = canvas_name
            self.test_scaled_width = scaled_width
            self.test_scaled_height = scaled_height

            if self.log_signal:
                self.log_signal.emit(f"PIL画布对象创建成功: {scaled_width}x{scaled_height}px (统一单位转换)")

            return True

        except ImportError:
            if self.log_signal:
                self.log_signal.emit("PIL库不可用，无法创建测试模式画布")
            return False
        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"创建PIL对象失败: {str(e)}")
            return False

    def _calculate_test_canvas_dimensions(self, canvas_width_px: int, canvas_height_px: int,
                                        miniature_ratio: float) -> Tuple[int, int]:
        """
        计算测试画布的缩放尺寸

        Args:
            canvas_width_px: 原始宽度
            canvas_height_px: 原始高度
            miniature_ratio: 缩小比率

        Returns:
            Tuple[int, int]: (缩放后宽度, 缩放后高度)
        """
        scaled_width = max(1, int(canvas_width_px * miniature_ratio))
        scaled_height = max(1, int(canvas_height_px * miniature_ratio))

        # 限制最大尺寸以避免内存问题
        max_test_size = 2000
        if scaled_width > max_test_size:
            scale_factor = max_test_size / scaled_width
            scaled_width = max_test_size
            scaled_height = max(1, int(scaled_height * scale_factor))

        if scaled_height > max_test_size:
            scale_factor = max_test_size / scaled_height
            scaled_height = max_test_size
            scaled_width = max(1, int(scaled_width * scale_factor))

        return scaled_width, scaled_height

    def _create_matplotlib_canvas_objects(self, scaled_width: int, scaled_height: int,
                                        canvas_name: str, miniature_ratio: float) -> bool:
        """
        创建 matplotlib 画布和绘图对象

        Args:
            scaled_width: 缩放后宽度
            scaled_height: 缩放后高度
            canvas_name: 画布名称
            miniature_ratio: 缩小比率

        Returns:
            bool: 是否创建成功
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            import matplotlib.font_manager as fm

            # 设置中文字体
            self._setup_chinese_font()

            # 创建图形和轴
            self.test_fig, self.test_ax = plt.subplots(figsize=(scaled_width/100, scaled_height/100), dpi=100)

            # 设置画布属性
            self.test_ax.set_xlim(0, scaled_width)
            self.test_ax.set_ylim(0, scaled_height)
            self.test_ax.set_aspect('equal')

            # 设置白色背景
            self.test_ax.set_facecolor('white')
            self.test_fig.patch.set_facecolor('white')

            # 移除坐标轴
            self.test_ax.set_xticks([])
            self.test_ax.set_yticks([])

            # 添加边框
            border = patches.Rectangle((0, 0), scaled_width, scaled_height,
                                     linewidth=2, edgecolor='black', facecolor='none')
            self.test_ax.add_patch(border)

            # 保存画布信息
            self.test_canvas_name = canvas_name
            self.test_miniature_ratio = miniature_ratio
            self.test_scaled_width = scaled_width
            self.test_scaled_height = scaled_height

            return True

        except ImportError:
            if self.log_signal:
                self.log_signal.emit("matplotlib库不可用，无法创建测试模式画布")
            return False
        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"创建 matplotlib 对象失败: {str(e)}")
            return False

    def _setup_chinese_font(self):
        """
        设置中文字体，解决中文乱码问题 - 增强版
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.font_manager as fm
            import platform
            import os

            # 根据操作系统选择合适的中文字体
            system = platform.system()

            if system == 'Windows':
                # Windows系统中文字体路径和名称
                font_configs = [
                    ('SimHei', ['C:/Windows/Fonts/simhei.ttf']),
                    ('Microsoft YaHei', ['C:/Windows/Fonts/msyh.ttc', 'C:/Windows/Fonts/msyh.ttf']),
                    ('SimSun', ['C:/Windows/Fonts/simsun.ttc', 'C:/Windows/Fonts/simsun.ttf']),
                    ('KaiTi', ['C:/Windows/Fonts/simkai.ttf']),
                    ('Microsoft JhengHei', ['C:/Windows/Fonts/msjh.ttc']),
                ]
            elif system == 'Darwin':  # macOS
                font_configs = [
                    ('PingFang SC', ['/System/Library/Fonts/PingFang.ttc']),
                    ('Heiti SC', ['/System/Library/Fonts/STHeiti Light.ttc']),
                    ('STHeiti', ['/System/Library/Fonts/STHeiti Medium.ttc']),
                    ('Arial Unicode MS', ['/Library/Fonts/Arial Unicode.ttf']),
                ]
            else:  # Linux
                font_configs = [
                    ('WenQuanYi Micro Hei', ['/usr/share/fonts/truetype/wqy/wqy-microhei.ttc']),
                    ('Droid Sans Fallback', ['/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf']),
                    ('Noto Sans CJK SC', ['/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc']),
                ]

            # 尝试设置字体
            font_set = False
            working_font = None

            for font_name, font_paths in font_configs:
                # 检查字体文件是否存在
                font_exists = any(os.path.exists(path) for path in font_paths)

                if font_exists:
                    try:
                        # 尝试设置字体
                        plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False

                        # 测试字体是否能正确显示中文
                        test_fig, test_ax = plt.subplots(figsize=(1, 1))
                        test_ax.text(0.5, 0.5, '测试', fontsize=12)
                        plt.close(test_fig)

                        font_set = True
                        working_font = font_name
                        if self.log_signal:
                            self.log_signal.emit(f"成功设置中文字体: {font_name}")
                        break

                    except Exception as e:
                        if self.log_signal:
                            self.log_signal.emit(f"字体 {font_name} 设置失败: {str(e)}")
                        continue

            if not font_set:
                # 如果都失败了，尝试系统默认字体
                try:
                    # 获取系统中所有可用的中文字体
                    available_fonts = [f.name for f in fm.fontManager.ttflist]
                    chinese_fonts = [f for f in available_fonts if any(keyword in f for keyword in
                                   ['SimHei', 'SimSun', 'Microsoft', 'YaHei', 'PingFang', 'Heiti', 'WenQuanYi', 'Noto'])]

                    if chinese_fonts:
                        plt.rcParams['font.sans-serif'] = chinese_fonts[:3] + ['DejaVu Sans']
                        plt.rcParams['axes.unicode_minus'] = False
                        working_font = chinese_fonts[0]
                        font_set = True
                        if self.log_signal:
                            self.log_signal.emit(f"使用系统检测到的中文字体: {working_font}")
                    else:
                        # 最后的备选方案
                        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False
                        if self.log_signal:
                            self.log_signal.emit("警告: 未找到中文字体，使用默认字体，中文可能显示为方块")

                except Exception as e:
                    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                    plt.rcParams['axes.unicode_minus'] = False
                    if self.log_signal:
                        self.log_signal.emit(f"字体检测失败: {str(e)}，使用默认字体")

            # 清除matplotlib字体缓存，确保新设置生效
            try:
                fm._rebuild()
            except:
                pass

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"设置中文字体失败: {str(e)}")

    def _load_chinese_font_for_pil(self, fontsize: int):
        """
        为PIL加载中文字体，解决中文乱码问题

        Args:
            fontsize: 字体大小

        Returns:
            ImageFont: 字体对象
        """
        try:
            from PIL import ImageFont
            import platform
            import os

            # 根据操作系统选择合适的中文字体路径
            system = platform.system()

            if system == 'Windows':
                # Windows系统中文字体路径
                font_paths = [
                    'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
                    'C:/Windows/Fonts/msyh.ttf',      # 微软雅黑
                    'C:/Windows/Fonts/simhei.ttf',    # 黑体
                    'C:/Windows/Fonts/simsun.ttc',    # 宋体
                    'C:/Windows/Fonts/simsun.ttf',    # 宋体
                    'C:/Windows/Fonts/simkai.ttf',    # 楷体
                    'C:/Windows/Fonts/msjh.ttc',      # 微软正黑体
                ]
            elif system == 'Darwin':  # macOS
                font_paths = [
                    '/System/Library/Fonts/PingFang.ttc',
                    '/System/Library/Fonts/STHeiti Light.ttc',
                    '/System/Library/Fonts/STHeiti Medium.ttc',
                    '/Library/Fonts/Arial Unicode.ttf',
                ]
            else:  # Linux
                font_paths = [
                    '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
                    '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',
                    '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
                    '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                ]

            # 尝试加载中文字体
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        font = ImageFont.truetype(font_path, fontsize)
                        if self.log_signal:
                            self.log_signal.emit(f"PIL成功加载中文字体: {os.path.basename(font_path)}")
                        return font
                    except Exception as e:
                        if self.log_signal:
                            self.log_signal.emit(f"PIL字体加载失败 {font_path}: {str(e)}")
                        continue

            # 如果都失败了，尝试系统默认字体
            try:
                # 尝试一些常见的字体名称
                font_names = ['arial.ttf', 'Arial.ttf', 'calibri.ttf', 'Calibri.ttf']
                for font_name in font_names:
                    try:
                        font = ImageFont.truetype(font_name, fontsize)
                        if self.log_signal:
                            self.log_signal.emit(f"PIL使用系统字体: {font_name}")
                        return font
                    except:
                        continue

                # 最后使用默认字体
                font = ImageFont.load_default()
                if self.log_signal:
                    self.log_signal.emit("PIL使用默认字体，中文可能显示为方块")
                return font

            except Exception as e:
                if self.log_signal:
                    self.log_signal.emit(f"PIL字体加载完全失败: {str(e)}")
                return ImageFont.load_default()

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"PIL字体处理异常: {str(e)}")
            try:
                from PIL import ImageFont
                return ImageFont.load_default()
            except:
                return None

    def _create_pil_canvas_objects(self, scaled_width: int, scaled_height: int,
                                  canvas_name: str, miniature_ratio: float) -> bool:
        """
        创建PIL画布对象 - 参照tetris算法逻辑

        Args:
            scaled_width: 缩放后宽度
            scaled_height: 缩放后高度
            canvas_name: 画布名称
            miniature_ratio: 缩小比率

        Returns:
            bool: 是否创建成功
        """
        try:
            from PIL import Image, ImageDraw, ImageFont

            # 创建PIL图像对象
            self.test_pil_image = Image.new('RGB', (scaled_width, scaled_height), 'white')
            self.test_pil_draw = ImageDraw.Draw(self.test_pil_image)

            # 绘制边框
            self.test_pil_draw.rectangle(
                [0, 0, scaled_width-1, scaled_height-1],
                outline='black',
                width=2
            )

            # 保存画布信息
            self.test_canvas_name = canvas_name
            self.test_miniature_ratio = miniature_ratio
            self.test_scaled_width = scaled_width
            self.test_scaled_height = scaled_height

            if self.log_signal:
                self.log_signal.emit(f"PIL画布对象创建成功: {scaled_width}x{scaled_height}px")

            return True

        except ImportError:
            if self.log_signal:
                self.log_signal.emit("PIL库不可用，无法创建测试模式画布")
            return False
        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"创建PIL对象失败: {str(e)}")
            return False

    def place_test_mode_image(self, image_info: Dict[str, Any]) -> bool:
        """
        在测试模式画布上放置色块图片 - 兼容性接口，调用PIL实现

        Args:
            image_info: 图片信息

        Returns:
            bool: 是否放置成功
        """
        return self.place_test_mode_image_with_pil(image_info)

    def place_test_mode_image_with_pil(self, image_info: Dict[str, Any]) -> bool:
        """
        在测试模式画布上放置色块图片 - 使用PIL，参照tetris算法逻辑

        Args:
            image_info: 图片信息

        Returns:
            bool: 是否放置成功
        """
        try:
            # 第一步：验证PIL画布状态
            if not self._validate_pil_canvas_state():
                return False

            # 第二步：提取和验证图片信息
            image_data = self._extract_test_image_data(image_info)
            if image_data is None:
                return False

            # 第三步：计算缩放后的尺寸和位置
            scaled_rect = self._calculate_test_image_rect(image_data)

            # 第四步：选择颜色和绘制矩形（使用图片索引确保相邻颜色不同）
            image_index = len(self.placed_images)  # 使用已放置图片数量作为索引
            color = self._get_test_image_color(image_data['image_class'], image_index)
            self._draw_pil_image_rectangle(scaled_rect, color)

            # 第五步：绘制文本（如果空间足够）
            self._draw_pil_image_text(scaled_rect, image_data)

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"测试模式PIL放置图片失败: {str(e)}")
            return False

    def _validate_test_canvas_state(self) -> bool:
        """
        验证测试画布状态

        Returns:
            bool: 画布状态是否有效
        """
        if not hasattr(self, 'test_fig') or self.test_fig is None:
            if self.log_signal:
                self.log_signal.emit("测试模式画布未创建")
            return False

        if not hasattr(self, 'test_ax') or self.test_ax is None:
            if self.log_signal:
                self.log_signal.emit("测试模式绘图对象未创建")
            return False

        return True

    def _validate_pil_canvas_state(self) -> bool:
        """
        验证PIL画布状态

        Returns:
            bool: 画布状态是否有效
        """
        if not hasattr(self, 'test_pil_image') or self.test_pil_image is None:
            if self.log_signal:
                self.log_signal.emit("测试模式PIL画布未创建")
            return False

        if not hasattr(self, 'test_pil_draw') or self.test_pil_draw is None:
            if self.log_signal:
                self.log_signal.emit("测试模式PIL绘图对象未创建")
            return False

        return True

    def _extract_test_image_data(self, image_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取和验证图片信息

        Args:
            image_info: 原始图片信息

        Returns:
            Dict[str, Any]: 提取的图片数据，或 None 如果无效
        """
        try:
            data = {
                'x': image_info.get('x', 0),
                'y': image_info.get('y', 0),
                'width': image_info.get('width', 0),
                'height': image_info.get('height', 0),
                'name': image_info.get('name', 'Unknown'),
                'image_class': image_info.get('image_class', 'C'),
                'rotated': image_info.get('need_rotation', False)
            }

            # 验证必要的数值字段
            if data['width'] <= 0 or data['height'] <= 0:
                if self.log_signal:
                    self.log_signal.emit(f"无效的图片尺寸: {data['width']}x{data['height']}px")
                return None

            return data

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"提取图片信息失败: {str(e)}")
            return None

    def _calculate_test_image_rect(self, image_data: Dict[str, Any]) -> Dict[str, int]:
        """
        计算测试模式下的图片矩形，使用统一的单位转换

        Args:
            image_data: 图片数据

        Returns:
            Dict[str, int]: 包含 x, y, width, height 的矩形信息
        """
        # 测试模式使用cm直接转px的统一处理方式
        # 坐标和尺寸已经通过统一的单位转换器处理，直接使用传入的值
        scaled_x = image_data['x']
        scaled_y = image_data['y']
        scaled_width = image_data['width']
        scaled_height = image_data['height']

        # 确保最小尺寸（至少1像素）
        scaled_width = max(1, scaled_width)
        scaled_height = max(1, scaled_height)

        # 验证坐标和尺寸的合理性
        if scaled_x < 0 or scaled_y < 0:
            if self.log_signal:
                self.log_signal.emit(f"警告: 图片坐标为负数: ({scaled_x}, {scaled_y})")
            scaled_x = max(0, scaled_x)
            scaled_y = max(0, scaled_y)

        return {
            'x': scaled_x,
            'y': scaled_y,
            'width': scaled_width,
            'height': scaled_height
        }

    def _get_test_image_color(self, image_class: str, image_index: int = None) -> Tuple[int, int, int]:
        """
        根据图片类别和索引获取颜色，确保相邻图片颜色不同

        Args:
            image_class: 图片类别
            image_index: 图片索引，用于生成不同颜色

        Returns:
            Tuple[int, int, int]: RGB 颜色值
        """
        # 定义丰富的颜色调色板，确保相邻颜色有明显对比
        color_palette = [
            (255, 107, 107),  # 红色
            (78, 205, 196),   # 青绿色
            (69, 183, 209),   # 蓝色
            (150, 206, 180),  # 浅绿色
            (255, 234, 167),  # 黄色
            (221, 160, 221),  # 紫色
            (152, 216, 200),  # 薤绿色
            (247, 220, 111),  # 金黄色
            (248, 196, 113),  # 橙色
            (133, 193, 233),  # 浅蓝色
            (255, 154, 162),  # 粉红色
            (160, 196, 255),  # 淡蓝色
            (189, 224, 254),  # 天蓝色
            (162, 155, 254),  # 淡紫色
            (255, 218, 185),  # 桃色
            (255, 175, 204),  # 樱花粉
            (174, 198, 207),  # 灰蓝色
            (119, 221, 119),  # 亮绿色
            (253, 253, 150),  # 柠檬黄
            (255, 177, 101),  # 珊瑚色
        ]

        # 如果有图片索引，使用索引选择颜色，确保相邻图片颜色不同
        if image_index is not None:
            return color_palette[image_index % len(color_palette)]

        # 如果没有索引，按类别分配颜色（保持兼容性）
        class_color_map = {
            'A': color_palette[0],  # 红色 - A类图片（宽幅类）
            'B': color_palette[1],  # 青绿色 - B类图片（宽幅约束类）
            'C': color_palette[2],  # 蓝色 - C类图片（其他图片）
        }
        return class_color_map.get(image_class, color_palette[2])  # 默认为蓝色

    def _draw_test_image_rectangle(self, rect: Dict[str, int], color: Tuple[int, int, int]):
        """
        绘制测试图片矩形

        Args:
            rect: 矩形信息
            color: 填充颜色
        """
        try:
            import matplotlib.patches as patches

            # 将RGB颜色转换为0-1范围
            normalized_color = (color[0]/255.0, color[1]/255.0, color[2]/255.0)

            # 创建矩形补丁
            rectangle = patches.Rectangle(
                (rect['x'], rect['y']),
                rect['width'],
                rect['height'],
                linewidth=1,
                edgecolor='black',
                facecolor=normalized_color,
                alpha=0.8
            )

            # 添加到轴
            self.test_ax.add_patch(rectangle)

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"绘制矩形失败: {str(e)}")

    def _draw_test_image_text(self, rect: Dict[str, int], image_data: Dict[str, Any]):
        """
        绘制测试图片文本（如果空间足够）

        Args:
            rect: 矩形信息
            image_data: 图片数据
        """
        # 只在空间足够时绘制文本
        if rect['width'] <= 30 or rect['height'] <= 15:
            return

        try:
            # 准备显示文本
            display_name = f"{image_data['name']}{'(R)' if image_data['rotated'] else ''}"

            # 计算文本位置（中心）
            text_x = rect['x'] + rect['width'] / 2
            text_y = rect['y'] + rect['height'] / 2

            # 计算合适的字体大小
            fontsize = min(rect['width'] / len(display_name) * 1.5, rect['height'] * 0.3, 12)
            fontsize = max(fontsize, 6)  # 最小字体大小

            # 绘制白色文本，使用中文字体
            self.test_ax.text(
                text_x, text_y,
                display_name,
                ha='center',  # 水平对齐
                va='center',  # 垂直对齐
                fontsize=fontsize,
                color='white',
                weight='bold',
                bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7)
            )

        except Exception as e:
            # 忽略所有字体相关错误，不影响主要功能
            if self.log_signal:
                self.log_signal.emit(f"绘制文本失败: {str(e)}")

    def _draw_pil_image_rectangle(self, rect: Dict[str, int], color: Tuple[int, int, int]):
        """
        使用PIL绘制测试图片矩形

        Args:
            rect: 矩形信息
            color: 填充颜色
        """
        try:
            # 绘制填充矩形
            self.test_pil_draw.rectangle(
                [rect['x'], rect['y'], rect['x'] + rect['width'], rect['y'] + rect['height']],
                fill=color,
                outline='black',
                width=1
            )

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"PIL绘制矩形失败: {str(e)}")

    def _draw_pil_image_text(self, rect: Dict[str, int], image_data: Dict[str, Any]):
        """
        使用PIL绘制测试图片文本（如果空间足够）

        Args:
            rect: 矩形信息
            image_data: 图片数据
        """
        # 只在空间足够时绘制文本
        if rect['width'] <= 30 or rect['height'] <= 15:
            return

        try:
            from PIL import ImageFont

            # 准备显示文本
            display_name = f"{image_data['name']}{'(R)' if image_data['rotated'] else ''}"

            # 计算文本位置（中心）
            text_x = rect['x'] + rect['width'] // 2
            text_y = rect['y'] + rect['height'] // 2

            # 计算合适的字体大小
            fontsize = min(rect['width'] // len(display_name) * 2, rect['height'] // 3, 16)
            fontsize = max(fontsize, 8)  # 最小字体大小

            # 尝试加载中文字体 - 增强版
            font = self._load_chinese_font_for_pil(fontsize)

            # 绘制白色文本
            self.test_pil_draw.text(
                (text_x, text_y),
                display_name,
                fill='white',
                font=font,
                anchor='mm'  # 中心对齐
            )

        except Exception as e:
            # 忽略所有字体相关错误，不影响主要功能
            if self.log_signal:
                self.log_signal.emit(f"PIL绘制文本失败: {str(e)}")

    def save_test_mode_canvas(self, output_path: str) -> bool:
        """
        保存测试模式画布为JPG文件 - 主入口，分解为更小模块

        Args:
            output_path: 输出路径

        Returns:
            bool: 是否保存成功
        """
        try:
            # 第一步：验证画布状态
            if not self._validate_test_canvas_for_save():
                return False

            # 第二步：验证和准备输出路径
            if not self._prepare_output_path(output_path):
                return False

            # 第三步：保存图片文件
            success = self._save_test_canvas_file(output_path)

            if success and self.log_signal:
                self.log_signal.emit(f"测试模式画布已保存: {output_path}")

            return success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"保存测试模式画布失败: {str(e)}")
            return False

    def _validate_test_canvas_for_save(self) -> bool:
        """
        验证测试画布是否可以保存

        Returns:
            bool: 是否可以保存
        """
        if not hasattr(self, 'test_fig') or self.test_fig is None:
            if self.log_signal:
                self.log_signal.emit("测试模式画布未创建")
            return False

        # 检查画布尺寸
        try:
            if hasattr(self, 'test_scaled_width') and hasattr(self, 'test_scaled_height'):
                width, height = self.test_scaled_width, self.test_scaled_height
                if width <= 0 or height <= 0:
                    if self.log_signal:
                        self.log_signal.emit(f"无效的画布尺寸: {width}x{height}px")
                    return False
            else:
                if self.log_signal:
                    self.log_signal.emit("画布尺寸信息丢失")
                return False
        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"获取画布尺寸失败: {str(e)}")
            return False

        return True

    def _prepare_output_path(self, output_path: str) -> bool:
        """
        验证和准备输出路径

        Args:
            output_path: 输出路径

        Returns:
            bool: 路径是否有效并已准备好
        """
        if not output_path or not output_path.strip():
            if self.log_signal:
                self.log_signal.emit("输出路径不能为空")
            return False

        try:
            import os

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                if self.log_signal:
                    self.log_signal.emit(f"创建输出目录: {output_dir}")

            # 检查文件扩展名
            if not output_path.lower().endswith(('.jpg', '.jpeg')):
                if self.log_signal:
                    self.log_signal.emit(f"警告: 输出文件扩展名不是JPG格式: {output_path}")

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"准备输出路径失败: {str(e)}")
            return False

    def _save_test_canvas_file(self, output_path: str) -> bool:
        """
        保存测试画布文件

        Args:
            output_path: 输出路径

        Returns:
            bool: 是否保存成功
        """
        try:
            import matplotlib.pyplot as plt

            # 保存为高质量JPG格式
            self.test_fig.savefig(
                output_path,
                format='jpeg',
                dpi=150,
                bbox_inches='tight',
                pad_inches=0.1,
                facecolor='white',
                edgecolor='none'
            )

            # 关闭图形以释放内存
            plt.close(self.test_fig)

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"保存图片文件失败: {str(e)}")
            return False

    def save_test_mode_canvas(self, output_path: str) -> bool:
        """
        保存测试模式画布 - 兼容性接口，调用PIL实现

        Args:
            output_path: 输出路径

        Returns:
            bool: 是否保存成功
        """
        return self.save_test_mode_canvas_with_pil(output_path)

    def save_test_mode_canvas_with_pil(self, output_path: str) -> bool:
        """
        使用PIL保存测试模式画布为JPG文件 - 参照tetris算法逻辑

        Args:
            output_path: 输出路径

        Returns:
            bool: 是否保存成功
        """
        try:
            # 第一步：验证PIL画布状态
            if not self._validate_pil_canvas_for_save():
                return False

            # 第二步：验证和准备输出路径
            if not self._prepare_output_path(output_path):
                return False

            # 第三步：保存PIL图片文件
            success = self._save_pil_canvas_file(output_path)

            if success and self.log_signal:
                self.log_signal.emit(f"测试模式PIL画布已保存: {output_path}")

            return success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"保存测试模式PIL画布失败: {str(e)}")
            return False

    def _validate_pil_canvas_for_save(self) -> bool:
        """
        验证PIL画布是否可以保存

        Returns:
            bool: 是否可以保存
        """
        if not hasattr(self, 'test_pil_image') or self.test_pil_image is None:
            if self.log_signal:
                self.log_signal.emit("测试模式PIL画布未创建")
            return False

        # 检查画布尺寸
        try:
            if hasattr(self, 'test_scaled_width') and hasattr(self, 'test_scaled_height'):
                width, height = self.test_scaled_width, self.test_scaled_height
                if width <= 0 or height <= 0:
                    if self.log_signal:
                        self.log_signal.emit(f"无效的PIL画布尺寸: {width}x{height}px")
                    return False
            else:
                if self.log_signal:
                    self.log_signal.emit("PIL画布尺寸信息丢失")
                return False
        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"获取PIL画布尺寸失败: {str(e)}")
            return False

        return True

    def _save_pil_canvas_file(self, output_path: str) -> bool:
        """
        保存PIL画布文件

        Args:
            output_path: 输出路径

        Returns:
            bool: 是否保存成功
        """
        try:
            # 保存为高质量JPG格式
            self.test_pil_image.save(
                output_path,
                'JPEG',
                quality=95,
                optimize=True
            )

            if self.log_signal:
                self.log_signal.emit(f"PIL图片文件保存成功: {output_path}")

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"保存PIL图片文件失败: {str(e)}")
            return False

    def generate_test_visualization(self, output_path: str, test_data: Dict[str, Any] = None) -> bool:
        """
        生成测试模式的可视化图片和文档

        Args:
            output_path: 输出路径
            test_data: 测试数据

        Returns:
            bool: 是否成功生成
        """
        try:
            from PIL import Image, ImageDraw, ImageFont
            import os

            # 创建画布图片
            canvas_image = Image.new('RGB', (self.bin_width, self.current_max_height), 'white')
            draw = ImageDraw.Draw(canvas_image)

            # 定义颜色
            colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                '#DDA0DD', '#98D8C8', '#F7DC6F', '#F8C471', '#85C1E9'
            ]

            # 绘制图片
            for i, img in enumerate(self.placed_images):
                color = colors[i % len(colors)]

                # 转换颜色
                color_rgb = tuple(int(color[j:j+2], 16) for j in (1, 3, 5))

                # 绘制矩形
                draw.rectangle(
                    [img['x'], img['y'], img['x'] + img['width'], img['y'] + img['height']],
                    fill=color_rgb,
                    outline='black',
                    width=1
                )

                # 添加文字标签
                name = img.get('name', f"Img_{i+1}")
                # 使用增强的中文字体加载
                font = self._load_chinese_font_for_pil(12)

                text_x = img['x'] + img['width'] // 2
                text_y = img['y'] + img['height'] // 2
                draw.text((text_x, text_y), name, fill='black', font=font, anchor='mm')

            # 保存图片
            canvas_image.save(output_path)

            # 生成文档
            doc_path = os.path.splitext(output_path)[0] + '_说明.txt'
            self._generate_test_documentation(doc_path, test_data)

            if self.log_signal:
                self.log_signal.emit(f"测试可视化已生成: {output_path}")
                self.log_signal.emit(f"测试文档已生成: {doc_path}")

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"生成测试可视化失败: {str(e)}")
            return False

    def generate_test_documentation(self, doc_path: str, test_data: Dict[str, Any] = None) -> bool:
        """
        生成详细的测试文档 - 兼容性接口，调用PIL实现

        Args:
            doc_path: 文档路径
            test_data: 测试数据

        Returns:
            bool: 是否生成成功
        """
        return self.generate_test_documentation_with_pil(doc_path, test_data)

    def generate_test_documentation_with_pil(self, doc_path: str, test_data: Dict[str, Any] = None) -> bool:
        """
        生成详细的测试文档 - 参照tetris算法逻辑

        Args:
            doc_path: 文档路径
            test_data: 测试数据

        Returns:
            bool: 是否生成成功
        """
        try:
            layout_info = self.get_layout_info()

            content = []
            content.append("# RectPack算法测试模式说明文档")
            content.append("")
            content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            content.append("")

            # 画布详情
            content.append("★ 画布详情")
            content.append("")
            content.append(f"- 画布宽度: {layout_info['container_width']}px")
            content.append(f"- 画布高度: {layout_info['container_height']}px")
            content.append(f"- 总面积: {layout_info['total_area']:,}px²")
            content.append(f"- 已用面积: {layout_info['used_area']:,}px²")
            content.append(f"- 利用率: {layout_info['utilization_percent']:.2f}%")
            content.append(f"- 图片间距: {self.image_spacing}px")
            content.append("")

            # 算法信息
            content.append("★ 算法信息")
            content.append("")
            content.append(f"- RectPack库可用: {'\u662f' if RECTPACK_AVAILABLE else '\u5426'}")
            content.append(f"- 旋转功能: {'\u542f\u7528' if self.rotation_enabled else '\u7981\u7528'}")
            content.append(f"- 排序策略: {self.sort_key}")
            content.append(f"- 装箱算法: {self.pack_algo}")
            content.append("")

            # 图片分类统计
            content.append("★ 图片分类统计")
            content.append("")

            class_counts = {'A': 0, 'B': 0, 'C': 0, 'Unknown': 0}
            rotated_count = 0
            tetris_count = 0
            rectpack_count = 0

            for img in self.placed_images:
                img_class = img.get('image_class', 'Unknown')
                class_counts[img_class] = class_counts.get(img_class, 0) + 1
                if img.get('rotated', False):
                    rotated_count += 1
                if img.get('algorithm') == 'tetris':
                    tetris_count += 1
                else:
                    rectpack_count += 1

            content.append(f"- A类图片: {class_counts['A']}张")
            content.append(f"- B类图片: {class_counts['B']}张")
            content.append(f"- C类图片: {class_counts['C']}张")
            content.append(f"- 未分类图片: {class_counts['Unknown']}张")
            content.append(f"- 总计: {layout_info['placed_count']}张")
            content.append(f"- 旋转图片: {rotated_count}张")
            content.append(f"- 俄罗斯方块算法: {tetris_count}张")
            content.append(f"- RectPack算法: {rectpack_count}张")
            content.append("")

            # 性能统计
            content.append("★ 性能统计")
            content.append("")
            content.append(f"- 成功放置图片数量: {layout_info['placed_count']}张")
            content.append(f"- 画布利用率: {layout_info['utilization_percent']:.2f}%")
            if layout_info['placed_count'] > 0:
                avg_area = layout_info['used_area'] / layout_info['placed_count']
                content.append(f"- 平均图片面积: {avg_area:.0f}px²")

            # 利用率评价
            utilization = layout_info['utilization_percent']
            if utilization >= 85:
                rating = "★★★★★ 优秀"
            elif utilization >= 75:
                rating = "★★★★☆ 良好"
            elif utilization >= 65:
                rating = "★★★☆☆ 中等"
            elif utilization >= 50:
                rating = "★★☆☆☆ 较差"
            else:
                rating = "★☆☆☆☆ 待优化"

            content.append(f"- 利用率评价: {rating}")
            content.append("")

            # 测试模式说明
            content.append("★ 测试模式说明")
            content.append("")
            content.append("测试模式下，使用PIL生成彩色方块替代实际图片，不启动Photoshop，提高测试效率。")
            content.append("彩色方块图片保存为JPG格式，缩小模型比率用于减小生成图片的尺寸。")
            if test_data and test_data.get('is_test_all_data', False):
                content.append("测试全部数据模式已开启，包括处理'未入库'的图片。")
            content.append("")
            content.append("★★ 色块说明")
            content.append("")
            content.append("使用丰富的颜色调色板，确保相邻图片颜色有明显对比，便于观察排列效果。")
            content.append("")
            content.append("颜色分配策略:")
            content.append("- 按放置顺序循环使用20种不同颜色")
            content.append("- 红色系: 红色、粉红色、樱花粉、桃色")
            content.append("- 蓝色系: 蓝色、浅蓝色、天蓝色、灰蓝色")
            content.append("- 绿色系: 青绿色、浅绿色、薤绿色、亮绿色")
            content.append("- 黄色系: 黄色、金黄色、橙色、柠檬黄")
            content.append("- 紫色系: 紫色、淡紫色、淡蓝色、珊瑚色")
            content.append("")
            content.append("传统分类颜色（兼容性）:")
            content.append("- 红色: A类图片（宽幅类）")
            content.append("- 青绿色: B类图片（宽幅约束类）")
            content.append("- 蓝色: C类图片（其他图片）")
            content.append("")

            # 详细排列信息
            content.append("★ 详细排列信息")
            content.append("")
            content.append("| 序号 | 图片名称 | 尺寸(px) | 位置(x,y) | 旋转 | 算法 | 颜色编码 |")
            content.append("|------|----------|----------|-----------|------|------|----------|")

            # 优先使用传入的图片信息，如果没有则使用内部的placed_images
            images_to_display = []
            if test_data and 'images_info' in test_data and test_data['images_info']:
                images_to_display = test_data['images_info']
            elif hasattr(self, 'placed_images') and self.placed_images:
                images_to_display = self.placed_images

            # 生成详细排列信息
            for i, img in enumerate(images_to_display, 1):
                name = img.get('name', f'Image_{i}')
                width = img.get('width', 0)
                height = img.get('height', 0)
                x = img.get('x', 0)
                y = img.get('y', 0)
                rotated = '是' if img.get('need_rotation', False) or img.get('rotated', False) else '否'
                image_class = img.get('image_class', 'C')

                # 获取颜色编码（使用索引确保相邻颜色不同）
                color_rgb = self._get_test_image_color(image_class, i-1)
                color_hex = f"#{color_rgb[0]:02X}{color_rgb[1]:02X}{color_rgb[2]:02X}"

                content.append(f"| {i} | {name} | {width}x{height} | ({x},{y}) | {rotated} | rectpack | {color_hex} |")

            colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                '#DDA0DD', '#98D8C8', '#F7DC6F', '#F8C471', '#85C1E9'
            ]

            for i, img in enumerate(self.placed_images):
                name = img.get('name', f'Image_{i+1}')
                size = f"{img['width']}x{img['height']}"
                position = f"({img['x']},{img['y']})"
                rotated = "是" if img.get('rotated', False) else "否"
                algorithm = img.get('algorithm', 'rectpack')
                color = colors[i % len(colors)]

                content.append(f"| {i+1} | {name} | {size} | {position} | {rotated} | {algorithm} | {color} |")

            content.append("")

            # 算法优化信息
            content.append("★ 算法优化信息")
            content.append("")
            content.append("★★ 俄罗斯方块式优化特性")
            content.append("")
            content.append("1. **天际线算法**: 实现真正的俄罗斯方块式堆叠")
            content.append("2. **智能间隙填充**: 消除不必要的水平和垂直间隙")
            content.append("3. **图片组合优化**: 寻找完美填充画布宽度的图片组合")
            content.append("4. **错位垂直排列**: 类似俄罗斯方块的紧密堆叠模式")
            content.append("5. **旋转优化**: 当水平放置失败时尝试90度旋转")
            content.append("")

            # 性能统计
            content.append("★ 性能统计")
            content.append("")

            # 使用传入的数据或内部数据
            if test_data and 'images_info' in test_data and test_data['images_info']:
                images_for_stats = test_data['images_info']
                utilization = test_data.get('utilization', layout_info.get('utilization_percent', 0))
            else:
                images_for_stats = getattr(self, 'placed_images', [])
                utilization = layout_info.get('utilization_percent', 0)

            content.append(f"- 成功放置图片数量: {len(images_for_stats)}张")
            content.append(f"- 画布利用率: {utilization:.2f}%")
            if len(images_for_stats) > 0:
                avg_area = sum(img.get('width', 0) * img.get('height', 0) for img in images_for_stats) / len(images_for_stats)
                content.append(f"- 平均图片面积: {avg_area:.0f}px²")
            else:
                content.append("- 平均图片面积: 0px²")

            # 利用率评价
            if utilization >= 85:
                rating = "★★★★★ 优秀"
            elif utilization >= 75:
                rating = "★★★★☆ 良好"
            elif utilization >= 65:
                rating = "★★★☆☆ 中等"
            elif utilization >= 50:
                rating = "★★☆☆☆ 较差"
            else:
                rating = "★☆☆☆☆ 待优化"

            content.append(f"- 利用率评价: {rating}")
            content.append("")

            # 优化建议
            content.append("★ 优化建议")
            content.append("")
            if utilization < 85:
                content.append("★★ 建议优化方向:")
                content.append("")
                content.append("1. 增加图片旋转优先级")
                content.append("2. 优化图片组合策略")
                content.append("3. 减少图片间距")
                content.append("4. 使用更高级的装箱算法")
            else:
                content.append("★★ 当前利用率已达到优秀水平，无需进一步优化。")
            content.append("")

            # 测试环境信息
            if test_data:
                content.append("★ 测试环境信息")
                content.append("")
                content.append(f"- 测试数据集: {test_data.get('dataset_name', '未知')}")
                content.append(f"- 测试模式: {'PIL彩色方块' if test_data.get('test_mode', True) else 'Photoshop实际图片'}")
                content.append(f"- 缩放比例: {test_data.get('scale_ratio', 1.0)}")
                content.append("")

            # 写入文件
            import os
            os.makedirs(os.path.dirname(doc_path), exist_ok=True)
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))

            if self.log_signal:
                self.log_signal.emit(f"测试文档已生成: {doc_path}")

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"生成测试文档失败: {str(e)}")
            return False

    # ==================== 正式环境支持 ====================

    def create_production_canvas_with_ps(self, canvas_width_cm: float, canvas_height_cm: float,
                                        canvas_name: str, ppi: int = None) -> bool:
        """
        在正式环境下使用Photoshop创建画布 - 主入口，参照tetris算法逻辑

        Args:
            canvas_width_cm: 画布宽度（厘米）
            canvas_height_cm: 画布高度（厘米）
            canvas_name: 画布名称
            ppi: 分辨率（从配置读取，默认72）

        Returns:
            bool: 是否创建成功
        """
        try:
            # 第一步：处理PPI参数 - 从配置读取
            if ppi is None:
                try:
                    from utils.config_manager_duckdb import ConfigManagerDuckDB
                    config_manager = ConfigManagerDuckDB()
                    canvas_settings = config_manager.get_canvas_settings()
                    ppi = canvas_settings.get('ppi', 72)
                    if self.log_signal:
                        self.log_signal.emit(f"从配置读取PPI参数: {ppi}")
                except Exception as e:
                    ppi = 72  # 默认值
                    if self.log_signal:
                        self.log_signal.emit(f"读取PPI配置失败，使用默认值: {ppi}, 错误: {str(e)}")

            # 第二步：验证参数
            if not self._validate_production_canvas_params(canvas_width_cm, canvas_height_cm, canvas_name, ppi):
                return False

            # 第二步：检查Photoshop连接
            if not self._check_photoshop_connection():
                return False

            # 第三步：创建 Photoshop 画布
            success = self._create_ps_canvas(canvas_width_cm, canvas_height_cm, canvas_name, ppi)

            if success:
                self._update_production_canvas_state(canvas_name, ppi)
                if self.log_signal:
                    self.log_signal.emit(f"正式环境画布创建成功: {canvas_name} ({canvas_width_cm}x{canvas_height_cm}cm, {ppi}PPI)")

            return success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"创建正式环境画布失败: {str(e)}")
            return False

    def _validate_production_canvas_params(self, canvas_width_cm: float, canvas_height_cm: float,
                                         canvas_name: str, ppi: int) -> bool:
        """
        验证正式环境画布参数

        Args:
            canvas_width_cm: 画布宽度
            canvas_height_cm: 画布高度
            canvas_name: 画布名称
            ppi: 分辨率

        Returns:
            bool: 参数是否有效
        """
        if canvas_width_cm <= 0 or canvas_height_cm <= 0:
            if self.log_signal:
                self.log_signal.emit(f"无效的画布尺寸: {canvas_width_cm}x{canvas_height_cm}cm")
            return False

        if not canvas_name or not canvas_name.strip():
            if self.log_signal:
                self.log_signal.emit("画布名称不能为空")
            return False

        if ppi <= 0 or ppi > 3000:  # 合理的PPI范围
            if self.log_signal:
                self.log_signal.emit(f"无效的分辨率: {ppi}PPI")
            return False

        # 检查画布尺寸是否过大
        max_width_cm = 300  # 3米
        max_height_cm = 500  # 5米
        if canvas_width_cm > max_width_cm or canvas_height_cm > max_height_cm:
            if self.log_signal:
                self.log_signal.emit(f"警告: 画布尺寸过大 ({canvas_width_cm}x{canvas_height_cm}cm)，可能影响性能")

        return True

    def _check_photoshop_connection(self) -> bool:
        """
        检查Photoshop连接

        Returns:
            bool: 连接是否正常
        """
        try:
            from utils.photoshop_helper import PhotoshopHelper

            success, message = PhotoshopHelper.check_photoshop()
            if not success:
                if self.log_signal:
                    self.log_signal.emit(f"Photoshop连接失败: {message}")
                return False

            return True

        except ImportError:
            if self.log_signal:
                self.log_signal.emit("PhotoshopHelper模块不可用")
            return False
        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"检查Photoshop连接时发生错误: {str(e)}")
            return False

    def _create_ps_canvas(self, canvas_width_cm: float, canvas_height_cm: float,
                         canvas_name: str, ppi: int) -> bool:
        """
        创建 Photoshop 画布

        Args:
            canvas_width_cm: 宽度
            canvas_height_cm: 高度
            canvas_name: 名称
            ppi: 分辨率

        Returns:
            bool: 是否创建成功
        """
        try:
            from utils.photoshop_helper import PhotoshopHelper

            success = PhotoshopHelper.create_canvas(
                width_cm=canvas_width_cm,
                height_cm=canvas_height_cm,
                name=canvas_name,
                ppi=ppi
            )

            if not success and self.log_signal:
                self.log_signal.emit("正式环境画布创建失败")

            return success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"调用Photoshop创建画布失败: {str(e)}")
            return False

    def _update_production_canvas_state(self, canvas_name: str, ppi: int):
        """
        更新正式环境画布状态

        Args:
            canvas_name: 画布名称
            ppi: 分辨率
        """
        self.production_canvas_name = canvas_name
        self.production_ppi = ppi

    def place_production_image(self, image_info: Dict[str, Any]) -> bool:
        """
        在正式环境画布上放置实际图片 - 按照RectPack算法的排列结果
        深度优化版本，确保正确按照RectPack布局逻辑放置图片

        Args:
            image_info: 图片信息，包含坐标、尺寸、旋转等信息

        Returns:
            bool: 是否放置成功
        """
        try:
            from utils.photoshop_helper import PhotoshopHelper
            import os

            # 获取图片信息 - 严格按照RectPack算法的排列结果
            image_path = image_info.get('path', '')
            x_px = image_info.get('x', 0)
            y_px = image_info.get('y', 0)
            width_px = image_info.get('width', 0)
            height_px = image_info.get('height', 0)

            # 处理旋转逻辑 - 支持RectPack的旋转决策
            need_rotation = image_info.get('need_rotation', False)
            rotated = image_info.get('rotated', False)
            rotation_angle = 90 if (need_rotation or rotated) else 0

            image_name = image_info.get('name', 'Unknown')

            # 验证图片文件存在性
            if not image_path or not os.path.exists(image_path):
                if self.log_signal:
                    self.log_signal.emit(f"图片文件不存在: {image_path}")
                return False

            # 验证坐标和尺寸的合理性
            if width_px <= 0 or height_px <= 0:
                if self.log_signal:
                    self.log_signal.emit(f"无效的图片尺寸: {image_name} ({width_px}x{height_px})")
                return False

            # 获取PPI参数，优先使用实例中的PPI，否则从配置读取
            ppi = getattr(self, 'production_ppi', None)
            if ppi is None:
                try:
                    from utils.config_manager_duckdb import ConfigManagerDuckDB
                    config_manager = ConfigManagerDuckDB()
                    canvas_settings = config_manager.get_canvas_settings()
                    ppi = canvas_settings.get('ppi', 72)
                except Exception:
                    ppi = 72  # 默认值

            # 使用RectPack布局逻辑放置图片 - 精确坐标和尺寸
            # 注意：PhotoshopHelper.place_image支持像素单位参数

            # 创建图片身份信息（如果唯一ID生成器可用）
            identity = self.create_image_identity(image_info)

            # 计算图层信息（修复索引计算错误）
            # 修复前问题：使用len(self.placed_images)时，此时placed_images可能还未更新
            # 修复方案：使用placement_count作为准确的索引
            layer_index = self.placement_count  # 使用已放置数量作为索引
            total_images = self.placement_count + 1  # 预计总图片数

            # 准备图层名称（使用唯一ID或默认命名）
            if identity:
                layer_name = identity.layer_name
                unique_id = identity.unique_id
            else:
                # 回退到默认命名
                layer_name = f"RectPack_Image_{layer_index + 1}_of_{total_images}"
                unique_id = None

            success = PhotoshopHelper.place_image(
                image_path=image_path,
                x=x_px,  # 直接使用像素坐标
                y=y_px,
                width=width_px,  # 直接使用像素尺寸
                height=height_px,
                rotation=rotation_angle,  # 使用RectPack的旋转决策
                ppi=ppi,
                layer_index=layer_index,  # 传递图层索引
                total_images=total_images,  # 传递总图片数
                layer_name=layer_name,  # 传递唯一图层名称
                unique_id=unique_id  # 传递唯一ID
            )

            if success:
                if self.log_signal:
                    rotation_info = f" (旋转{rotation_angle}°)" if rotation_angle > 0 else ""
                    self.log_signal.emit(f"✅ 放置成功: {image_name} at ({x_px},{y_px}) {width_px}x{height_px}px{rotation_info}")
                return True
            else:
                if self.log_signal:
                    self.log_signal.emit(f"❌ 放置失败: {image_name} at ({x_px},{y_px}) {width_px}x{height_px}px")
                return False

        except Exception as e:
            error_msg = f"放置图片异常: {image_info.get('name', 'Unknown')} - {str(e)}"
            if self.log_signal:
                self.log_signal.emit(f"❌ {error_msg}")
            return False

    def save_production_canvas_as_tiff(self, output_path: str) -> bool:
        """
        保存正式环境画布为TIFF文件

        Args:
            output_path: 输出路径

        Returns:
            bool: 是否保存成功
        """
        try:
            from utils.photoshop_helper import PhotoshopHelper

            # 确保输出目录存在
            import os
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 保存为TIFF格式
            success = PhotoshopHelper.save_as_tiff(output_path)

            if success:
                if self.log_signal:
                    self.log_signal.emit(f"正式环境画布已保存: {output_path}")
                return True
            else:
                if self.log_signal:
                    self.log_signal.emit("保存正式环境画布失败")
                return False

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"保存正式环境画布失败: {str(e)}")
            return False

    def generate_production_documentation_with_ps(self, doc_path: str, production_data: Dict[str, Any] = None) -> bool:
        """
        生成正式环境的详细文档 - 参照tetris算法逻辑

        Args:
            doc_path: 文档路径
            production_data: 正式环境数据

        Returns:
            bool: 是否生成成功
        """
        try:
            layout_info = self.get_layout_info()

            content = []
            content.append("# RectPack算法正式环境说明文档")
            content.append("")
            content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            content.append("")

            # 正式环境信息
            content.append("★ 正式环境信息")
            content.append("")
            content.append("正式环境下，使用Photoshop进行实际图片排版，生成高质量TIFF格式输出。")
            content.append("支持高分辨率输出，适用于印刷和专业设计工作。")
            content.append("")

            # 画布详情
            content.append("★ 画布详情")
            content.append("")

            # 获取PPI参数用于单位转换
            ppi = production_data.get('ppi') if production_data else getattr(self, 'production_ppi', 72)
            if ppi is None:
                ppi = 72

            # 正确的单位转换：像素 / (PPI / 2.54) = 厘米
            width_cm = layout_info['container_width'] / (ppi / 2.54)
            height_cm = layout_info['container_height'] / (ppi / 2.54)

            content.append(f"- 画布宽度: {layout_info['container_width']}px ({width_cm:.1f}cm)")
            content.append(f"- 画布高度: {layout_info['container_height']}px ({height_cm:.1f}cm)")
            content.append(f"- 总面积: {layout_info['total_area']:,}px²")
            content.append(f"- 已用面积: {layout_info['used_area']:,}px²")
            content.append(f"- 利用率: {layout_info['utilization_percent']:.2f}%")
            content.append(f"- 图片间距: {self.image_spacing}px")
            content.append(f"- 分辨率: {ppi}PPI")
            content.append("")

            # 正式环境设置
            content.append("★ 正式环境设置")
            content.append("")
            content.append(f"- Photoshop版本: {production_data.get('ps_version', '未知') if production_data else '未知'}")
            content.append(f"- 输出格式: TIFF")
            content.append(f"- 颜色模式: {production_data.get('color_mode', 'RGB') if production_data else 'RGB'}")
            content.append(f"- 压缩方式: {production_data.get('compression', 'LZW') if production_data else 'LZW'}")
            content.append("")

            # 图片分类统计
            content.append("★ 图片分类统计")
            content.append("")

            class_counts = {'A': 0, 'B': 0, 'C': 0, 'Unknown': 0}
            rotated_count = 0
            tetris_count = 0
            rectpack_count = 0

            for img in self.placed_images:
                img_class = img.get('image_class', 'Unknown')
                class_counts[img_class] = class_counts.get(img_class, 0) + 1
                if img.get('rotated', False):
                    rotated_count += 1
                if img.get('algorithm') == 'tetris':
                    tetris_count += 1
                else:
                    rectpack_count += 1

            content.append(f"- A类图片: {class_counts['A']}张")
            content.append(f"- B类图片: {class_counts['B']}张")
            content.append(f"- C类图片: {class_counts['C']}张")
            content.append(f"- 未分类图片: {class_counts['Unknown']}张")
            content.append(f"- 总计: {layout_info['placed_count']}张")
            content.append(f"- 旋转图片: {rotated_count}张")
            content.append(f"- 俄罗斯方块算法: {tetris_count}张")
            content.append(f"- RectPack算法: {rectpack_count}张")
            content.append("")

            # 详细排列信息
            content.append("★ 详细排列信息")
            content.append("")
            content.append("| 序号 | 图片名称 | 尺寸(px) | 尺寸(cm) | 位置(x,y) | 旋转 | 算法 | 类别 |")
            content.append("|------|----------|----------|----------|-----------|------|------|------|")

            for i, img in enumerate(self.placed_images):
                name = img.get('name', f'Image_{i+1}')
                size_px = f"{img['width']}x{img['height']}"
                # 使用正确的PPI转换
                width_cm = img['width'] / (ppi / 2.54)
                height_cm = img['height'] / (ppi / 2.54)
                size_cm = f"{width_cm:.1f}x{height_cm:.1f}"
                position = f"({img['x']},{img['y']})"
                rotated = "是" if img.get('rotated', False) else "否"
                algorithm = img.get('algorithm', 'rectpack')
                img_class = img.get('image_class', 'Unknown')

                content.append(f"| {i+1} | {name} | {size_px} | {size_cm} | {position} | {rotated} | {algorithm} | {img_class} |")

            content.append("")

            # 性能统计
            content.append("★ 性能统计")
            content.append("")
            content.append(f"- 成功放置图片数量: {layout_info['placed_count']}张")
            content.append(f"- 画布利用率: {layout_info['utilization_percent']:.2f}%")
            if layout_info['placed_count'] > 0:
                avg_area = layout_info['used_area'] / layout_info['placed_count']
                content.append(f"- 平均图片面积: {avg_area:.0f}px²")

            # 利用率评价
            utilization = layout_info['utilization_percent']
            if utilization >= 85:
                rating = "★★★★★ 优秀"
            elif utilization >= 75:
                rating = "★★★★☆ 良好"
            elif utilization >= 65:
                rating = "★★★☆☆ 中等"
            elif utilization >= 50:
                rating = "★★☆☆☆ 较差"
            else:
                rating = "★☆☆☆☆ 待优化"

            content.append(f"- 利用率评价: {rating}")
            content.append("")

            # 正式环境优势
            content.append("★ 正式环境优势")
            content.append("")
            content.append("★★ 专业级输出:")
            content.append("")
            content.append("1. 高分辨率TIFF格式输出")
            content.append("2. 支持专业印刷标准")
            content.append("3. 完整的颜色管理")
            content.append("4. 无损压缩保证质量")
            content.append("5. Photoshop原生支持")
            content.append("")

            # 正式环境信息
            if production_data:
                content.append("★ 正式环境信息")
                content.append("")
                content.append(f"- 画布名称: {production_data.get('canvas_name', '未知')}")
                content.append(f"- 材料名称: {production_data.get('material_name', '未知')}")
                content.append(f"- 画布序号: {production_data.get('canvas_sequence', 1)}")
                content.append(f"- 处理模式: Photoshop实际图片")
                content.append("")

            # 写入文件
            import os
            os.makedirs(os.path.dirname(doc_path), exist_ok=True)
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))

            if self.log_signal:
                self.log_signal.emit(f"正式环境文档已生成: {doc_path}")

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"生成正式环境文档失败: {str(e)}")
            return False

    def create_complete_production_environment(self, arranged_images: List[Dict[str, Any]],
                                             canvas_name: str, material_name: str,
                                             canvas_sequence: int, output_dir: str,
                                             ppi: int = None) -> bool:
        """
        创建完整的正式环境 - 完全参照tetris算法的image_processor架构
        深度优化版本，使用统一的image_processor接口

        Args:
            arranged_images: 已排列的图片列表
            canvas_name: 画布名称
            material_name: 材料名称
            canvas_sequence: 画布序号
            output_dir: 输出目录
            ppi: 分辨率

        Returns:
            bool: 是否成功
        """
        try:
            if not arranged_images:
                if self.log_signal:
                    self.log_signal.emit("错误: 没有图片需要处理")
                return False

            # 使用image_processor架构 - 完全参照tetris算法
            from utils.image_processor import get_image_processor
            import os
            # import time  # 已替换为统一的时间处理工具

            # 阶段1：获取画布尺寸和验证
            if self.log_signal:
                self.log_signal.emit("🔍 阶段1: 获取画布尺寸...")

            layout_info = self.get_layout_info()
            canvas_width_px = layout_info['container_width']
            canvas_height_px = layout_info['container_height']

            if canvas_width_px <= 0 or canvas_height_px <= 0:
                if self.log_signal:
                    self.log_signal.emit(f"错误: 无效的画布尺寸 {canvas_width_px}x{canvas_height_px}px")
                return False

            # 阶段2：处理PPI参数和初始化image_processor
            if self.log_signal:
                self.log_signal.emit("📏 阶段2: 初始化image_processor...")

            if ppi is None:
                try:
                    from utils.config_manager_duckdb import ConfigManagerDuckDB
                    config_manager = ConfigManagerDuckDB()
                    canvas_settings = config_manager.get_canvas_settings()
                    ppi = canvas_settings.get('ppi', 72)
                    if self.log_signal:
                        self.log_signal.emit(f"从配置读取PPI: {ppi}")
                except Exception as e:
                    ppi = 72
                    if self.log_signal:
                        self.log_signal.emit(f"使用默认PPI: {ppi} (配置读取失败: {str(e)})")

            # 初始化image_processor - 完全参照tetris算法
            image_processor = get_image_processor(is_test_mode=False, config={})

            if self.log_signal:
                self.log_signal.emit(f"✅ 画布尺寸: {canvas_width_px}x{canvas_height_px}px, PPI={ppi}")
                self.log_signal.emit(f"✅ 待处理图片: {len(arranged_images)} 张")

            # 阶段3：创建画布 - 使用image_processor
            if self.log_signal:
                self.log_signal.emit("🎨 阶段3: 创建画布...")

            full_canvas_name = f"{canvas_name}_{canvas_sequence}"
            success = image_processor.create_canvas(
                width=canvas_width_px,
                height=canvas_height_px,
                name=full_canvas_name,
                ppi=ppi
            )

            if not success:
                if self.log_signal:
                    self.log_signal.emit("正式环境画布创建失败")
                return False

            if self.log_signal:
                self.log_signal.emit(f"✅ 画布创建成功: {full_canvas_name}")

            # 阶段4：按RectPack算法放置所有图片 - 使用image_processor
            if self.log_signal:
                self.log_signal.emit(f"🖼️ 阶段4: 按RectPack算法放置 {len(arranged_images)} 张图片...")

            placed_count = 0
            failed_images = []

            for i, image_info in enumerate(arranged_images):
                try:
                    # 准备图片信息 - 完全参照tetris算法的数据结构
                    image_path = image_info.get('path', '')
                    x = image_info.get('x', 0)
                    y = image_info.get('y', 0)
                    width = image_info.get('width', 0)
                    height = image_info.get('height', 0)
                    need_rotation = image_info.get('need_rotation', False) or image_info.get('rotated', False)
                    image_name = image_info.get('name', f'Image_{i+1}')
                    image_class = image_info.get('image_class', 'C')  # RectPack默认为C类

                    # 创建完整的图片信息字典 - 与tetris算法保持一致
                    place_info = {
                        'image_path': image_path,
                        'x': x,
                        'y': y,
                        'width': width,
                        'height': height,
                        'rotated': need_rotation,
                        'name': image_name,
                        'image_class': image_class
                    }

                    # 使用image_processor放置图片 - 完全参照tetris算法
                    success = image_processor.place_image(place_info)

                    if success:
                        placed_count += 1
                        # 每10张图片显示一次进度
                        if (i + 1) % 10 == 0 or i == 0:
                            if self.log_signal:
                                self.log_signal.emit(f"📊 已放置: {placed_count}/{len(arranged_images)} 张图片")
                    else:
                        failed_images.append(image_name)
                        if self.log_signal:
                            self.log_signal.emit(f"⚠️ 放置图片失败: {image_name}")

                except Exception as e:
                    image_name = image_info.get('name', f'Image_{i+1}')
                    failed_images.append(image_name)
                    if self.log_signal:
                        self.log_signal.emit(f"⚠️ 放置图片异常: {image_name} - {str(e)}")

            # 放置结果统计
            success_rate = (placed_count / len(arranged_images) * 100) if arranged_images else 0
            if self.log_signal:
                self.log_signal.emit(f"✅ 图片放置完成: {placed_count}/{len(arranged_images)} 张成功 ({success_rate:.1f}%)")
                if failed_images:
                    self.log_signal.emit(f"⚠️ 失败图片: {', '.join(failed_images[:3])}{'...' if len(failed_images) > 3 else ''}")

            # 阶段5：保存TIFF文件 - 使用image_processor
            if self.log_signal:
                self.log_signal.emit("💾 阶段5: 保存TIFF文件...")

            # 确保输出目录存在
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                if self.log_signal:
                    self.log_signal.emit(f"📁 创建输出目录: {output_dir}")

            tiff_filename = f"{material_name}_{canvas_sequence}.tif"
            tiff_path = os.path.join(output_dir, tiff_filename)

            # 使用image_processor保存画布 - 完全参照tetris算法
            success = image_processor.save_canvas(tiff_path)
            if not success:
                if self.log_signal:
                    self.log_signal.emit(f"保存TIFF文件失败: {tiff_path}")
                return False

            # 验证TIFF文件是否成功保存
            if os.path.exists(tiff_path):
                file_size = os.path.getsize(tiff_path)
                if self.log_signal:
                    self.log_signal.emit(f"✅ TIFF文件保存成功: {tiff_filename} ({file_size:,} bytes)")
            else:
                if self.log_signal:
                    self.log_signal.emit(f"❌ TIFF文件保存验证失败: {tiff_path}")
                return False

            # 阶段6：生成详细说明文档（优化版）
            if self.log_signal:
                self.log_signal.emit("📄 阶段6: 生成详细说明文档...")

            doc_filename = f"{material_name}_{canvas_sequence}_说明.md"
            doc_path = os.path.join(output_dir, doc_filename)

            # 准备详细画布信息
            canvas_info = {
                'canvas_name': full_canvas_name,
                'material_name': material_name,
                'canvas_sequence': canvas_sequence,
                'canvas_width_px': canvas_width_px,
                'canvas_height': canvas_height_px,
                'horizontal_expansion_cm': 0,  # RectPack算法没有水平拓展
                'max_height_cm': canvas_height_px / (ppi / 2.54),  # 转换为厘米
                'unit_conversion': 'cm_to_px_direct',  # 单位转换方式
                'test_all_data': False,  # 生产环境不是测试数据
                'ppi': ppi,
                'generation_time': self._get_current_time_string()
            }

            # 使用专用的文档生成器
            from core.rectpack_documentation_generator import create_rectpack_documentation

            success = create_rectpack_documentation(
                canvas_info=canvas_info,
                images_info=arranged_images,
                output_path=doc_path
            )

            if success:
                if self.log_signal:
                    self.log_signal.emit(f"✅ 详细说明文档生成成功: {doc_filename}")
            else:
                if self.log_signal:
                    self.log_signal.emit("⚠️ 警告: 生成详细说明文档失败")

            # 阶段7：关闭画布 - 完全参照tetris算法
            if self.log_signal:
                self.log_signal.emit("🔒 阶段7: 关闭画布...")
            image_processor.close_canvas()

            # 阶段8：清理资源 - 完全参照tetris算法
            image_processor.cleanup()

            # 阶段9：最终统计和完成信息
            if self.log_signal:
                self.log_signal.emit("📈 阶段9: 最终统计...")
                self.log_signal.emit("" + "="*60)
                self.log_signal.emit("🎉 RectPack正式环境处理完成!")
                self.log_signal.emit(f"  • 画布名称: {full_canvas_name}")
                self.log_signal.emit(f"  • 材料名称: {material_name}")
                self.log_signal.emit(f"  • 画布尺寸: {canvas_width_px}x{canvas_height_px}px, PPI={ppi}")
                self.log_signal.emit(f"  • 成功放置: {placed_count}/{len(arranged_images)} 张图片 ({success_rate:.1f}%)")
                if failed_images:
                    self.log_signal.emit(f"  • 失败图片: {len(failed_images)} 张")
                self.log_signal.emit(f"  • TIFF文件: {tiff_filename}")
                self.log_signal.emit(f"  • 详细说明文档: {doc_filename}")
                self.log_signal.emit(f"  • 文档格式: 生产环境详细说明（包含完整图片排列信息）")
                self.log_signal.emit(f"  • 输出目录: {output_dir}")
                self.log_signal.emit(f"  • 使用架构: image_processor (参照tetris算法)")
                self.log_signal.emit("" + "="*60)

            # 最终成功判断
            if placed_count == 0:
                if self.log_signal:
                    self.log_signal.emit("❌ 没有任何图片成功放置，流程失败")
                return False

            return True

        except Exception as e:
            error_msg = f"创建正式环境失败: {str(e)}"
            if self.log_signal:
                self.log_signal.emit(f"❌ {error_msg}")
            return False

    # ==================== 公共接口方法 ====================

    def use_tetris_algorithm(self, enabled: bool = True):
        """
        切换是否使用俄罗斯方块算法

        Args:
            enabled: 是否启用俄罗斯方块算法
        """
        self.tetris_enabled = enabled
        if self.log_signal:
            status = "启用" if enabled else "禁用"
            self.log_signal.emit(f"俄罗斯方块算法已{status}")

    def place_image_with_algorithm_choice(self, width: int, height: int, image_data: Dict[str, Any] = None,
                                        force_tetris: bool = False) -> Tuple[int, int, bool]:
        """
        根据选择使用不同的算法放置图片

        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片数据
            force_tetris: 强制使用俄罗斯方块算法

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 判断是否使用俄罗斯方块算法
        use_tetris = force_tetris or getattr(self, 'tetris_enabled', False)

        # 对于C类图片，默认使用俄罗斯方块算法
        if image_data and image_data.get('image_class') == 'C':
            use_tetris = True

        if use_tetris:
            return self.tetris_place_image(width, height, image_data)
        else:
            return self.place_image(width, height, image_data)

    def _load_algorithm_parameters(self):
        """从配置加载算法参数"""
        try:
            from utils.config_manager_duckdb import ConfigManagerDuckDB
            config_manager = ConfigManagerDuckDB()
            rectpack_settings = config_manager.get_rectpack_settings()

            # 基础算法参数
            self.rotation_enabled = rectpack_settings.get('rectpack_rotation_enabled', True)
            self.sort_key = rectpack_settings.get('rectpack_sort_strategy', SORT_AREA)
            self.pack_algo = rectpack_settings.get('rectpack_pack_algorithm', BinPack.BNF)

            # 高级算法参数
            self.bin_selection_strategy = rectpack_settings.get('rectpack_bin_selection_strategy', 0)
            self.split_heuristic = rectpack_settings.get('rectpack_split_heuristic', 0)
            self.free_rect_choice = rectpack_settings.get('rectpack_free_rect_choice', 0)

            if self.log_signal:
                self.log_signal.emit(f"加载算法参数: 旋转={'启用' if self.rotation_enabled else '禁用'}, 排序策略={self.sort_key}")

        except Exception as e:
            # 使用默认值
            self.rotation_enabled = True
            self.sort_key = SORT_AREA
            self.pack_algo = BinPack.BNF
            self.bin_selection_strategy = 0
            self.split_heuristic = 0
            self.free_rect_choice = 0

            if self.log_signal:
                self.log_signal.emit(f"加载算法参数失败，使用默认值: {str(e)}")

    def _load_performance_parameters(self):
        """从配置加载性能参数"""
        try:
            from utils.config_manager_duckdb import ConfigManagerDuckDB
            config_manager = ConfigManagerDuckDB()
            rectpack_settings = config_manager.get_rectpack_settings()

            # 性能参数
            self.max_processing_time = rectpack_settings.get('rectpack_max_processing_time', 300)
            self.batch_size = rectpack_settings.get('rectpack_batch_size', 100)
            self.memory_limit_mb = rectpack_settings.get('rectpack_memory_limit_mb', 1024)
            self.enable_parallel = rectpack_settings.get('rectpack_enable_parallel', False)

            # 调试参数
            self.debug_mode = rectpack_settings.get('rectpack_debug_mode', False)
            self.log_level = rectpack_settings.get('rectpack_log_level', 1)
            self.save_intermediate_results = rectpack_settings.get('rectpack_save_intermediate_results', False)
            self.visualization_enabled = rectpack_settings.get('rectpack_visualization_enabled', False)

            # 根据调试级别调整日志输出
            if self.log_level == 0:
                self.enable_detailed_logging = False
                self.fast_mode = True
            elif self.log_level == 1:
                self.enable_detailed_logging = True
                self.fast_mode = False
            else:  # log_level >= 2
                self.enable_detailed_logging = True
                self.fast_mode = False

        except Exception as e:
            # 使用默认值
            self.max_processing_time = 300
            self.batch_size = 100
            self.memory_limit_mb = 1024
            self.enable_parallel = False
            self.debug_mode = False
            self.log_level = 1
            self.save_intermediate_results = False
            self.visualization_enabled = False

            if self.log_signal:
                self.log_signal.emit(f"加载性能参数失败，使用默认值: {str(e)}")

    def _load_optimization_parameters(self):
        """从配置加载优化参数"""
        try:
            from utils.config_manager_duckdb import ConfigManagerDuckDB
            config_manager = ConfigManagerDuckDB()
            rectpack_settings = config_manager.get_rectpack_settings()

            # 优化参数
            self.enable_optimization = rectpack_settings.get('rectpack_enable_optimization', True)
            self.optimization_iterations = rectpack_settings.get('rectpack_optimization_iterations', 5)
            self.min_utilization_threshold = rectpack_settings.get('rectpack_min_utilization_threshold', 85.0)
            self.rotation_penalty = rectpack_settings.get('rectpack_rotation_penalty', 0.05)
            self.aspect_ratio_preference = rectpack_settings.get('rectpack_aspect_ratio_preference', 1.0)

            if self.log_signal and self.debug_mode:
                self.log_signal.emit(f"加载优化参数: 优化={'启用' if self.enable_optimization else '禁用'}, 迭代次数={self.optimization_iterations}")

        except Exception as e:
            # 使用默认值
            self.enable_optimization = True
            self.optimization_iterations = 5
            self.min_utilization_threshold = 85.0
            self.rotation_penalty = 0.05
            self.aspect_ratio_preference = 1.0

            if self.log_signal:
                self.log_signal.emit(f"加载优化参数失败，使用默认值: {str(e)}")

    def reload_parameters(self):
        """重新加载所有参数"""
        self._load_algorithm_parameters()
        self._load_performance_parameters()
        self._load_optimization_parameters()

        # 重新初始化packer以应用新参数
        self._initialize_packer(silent=True)

        if self.log_signal:
            self.log_signal.emit("已重新加载所有RectPack参数")

    def get_current_parameters(self) -> Dict[str, Any]:
        """获取当前参数设置

        Returns:
            当前参数设置字典
        """
        return {
            # 算法参数
            'rotation_enabled': self.rotation_enabled,
            'sort_key': self.sort_key,
            'pack_algo': self.pack_algo,
            'bin_selection_strategy': getattr(self, 'bin_selection_strategy', 0),
            'split_heuristic': getattr(self, 'split_heuristic', 0),
            'free_rect_choice': getattr(self, 'free_rect_choice', 0),

            # 性能参数
            'max_processing_time': getattr(self, 'max_processing_time', 300),
            'batch_size': getattr(self, 'batch_size', 100),
            'memory_limit_mb': getattr(self, 'memory_limit_mb', 1024),
            'enable_parallel': getattr(self, 'enable_parallel', False),

            # 调试参数
            'debug_mode': getattr(self, 'debug_mode', False),
            'log_level': getattr(self, 'log_level', 1),
            'save_intermediate_results': getattr(self, 'save_intermediate_results', False),
            'visualization_enabled': getattr(self, 'visualization_enabled', False),

            # 优化参数
            'enable_optimization': getattr(self, 'enable_optimization', True),
            'optimization_iterations': getattr(self, 'optimization_iterations', 5),
            'min_utilization_threshold': getattr(self, 'min_utilization_threshold', 85.0),
            'rotation_penalty': getattr(self, 'rotation_penalty', 0.05),
            'aspect_ratio_preference': getattr(self, 'aspect_ratio_preference', 1.0)
        }

    # ============================================================================
    # matplotlib测试模式相关方法
    # ============================================================================

    def create_matplotlib_test_mode(self, output_path: str,
                                   base_width: int = None,
                                   horizontal_expansion: int = 0,
                                   title: str = "RectPack算法matplotlib测试模式") -> bool:
        """
        创建 matplotlib 测试模式，统一使用px单位

        Args:
            output_path: 输出文件路径
            base_width: 基础宽度（px），默认使用容器宽度
            horizontal_expansion: 水平拓展（px）
            title: 图形标题

        Returns:
            bool: 是否创建成功
        """
        if not MATPLOTLIB_AVAILABLE:
            if self.log_signal:
                self.log_signal.emit("错误: matplotlib工具模块不可用，无法创建matplotlib测试模式")
            return False

        try:
            # 准备容器配置
            if base_width is None:
                base_width = self.bin_width

            container_config = create_container_config_with_expansion(
                base_width=base_width,
                horizontal_expansion=horizontal_expansion,
                max_height=self.bin_height if self.bin_height != 999999999 else 5000,
                spacing=self.image_spacing
            )

            # 准备px数据（已经是px单位，不需要转换）
            px_data = []
            for img in self.placed_images:
                px_img = {
                    'id': img.get('id', img.get('rect_id', len(px_data) + 1)),
                    'name': img.get('name', f"Image_{len(px_data) + 1}"),
                    'width': img['width'],
                    'height': img['height'],
                    'x': img['x'],
                    'y': img['y'],
                    'image_class': img.get('image_class', 'C'),
                    'rotated': img.get('rotated', False),
                    'need_rotation': img.get('need_rotation', False)
                }
                px_data.append(px_img)

            # 创建matplotlib测试模式
            success = create_matplotlib_test_mode(
                px_data=px_data,
                container_config=container_config,
                output_path=output_path,
                title=title
            )

            if success and self.log_signal:
                self.log_signal.emit(f"matplotlib测试模式创建成功: {output_path}")

            return success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"创建matplotlib测试模式失败: {str(e)}")
            log.error(f"创建matplotlib测试模式失败: {str(e)}")
            return False

    def generate_matplotlib_documentation(self, doc_path: str,
                                        base_width: int = None,
                                        horizontal_expansion: int = 0) -> bool:
        """
        生成matplotlib模式的详细文档

        Args:
            doc_path: 文档输出路径
            base_width: 基础宽度（px），默认使用容器宽度
            horizontal_expansion: 水平拓展（px）

        Returns:
            bool: 是否生成成功
        """
        if not MATPLOTLIB_AVAILABLE:
            if self.log_signal:
                self.log_signal.emit("错误: matplotlib工具模块不可用，无法生成matplotlib文档")
            return False

        try:
            # 准备容器配置
            if base_width is None:
                base_width = self.bin_width

            container_config = create_container_config_with_expansion(
                base_width=base_width,
                horizontal_expansion=horizontal_expansion,
                max_height=self.bin_height if self.bin_height != 999999999 else 5000,
                spacing=self.image_spacing
            )

            # 准备px数据
            px_data = []
            for img in self.placed_images:
                px_img = {
                    'id': img.get('id', img.get('rect_id', len(px_data) + 1)),
                    'name': img.get('name', f"Image_{len(px_data) + 1}"),
                    'width': img['width'],
                    'height': img['height'],
                    'x': img['x'],
                    'y': img['y'],
                    'image_class': img.get('image_class', 'C'),
                    'rotated': img.get('rotated', False),
                    'need_rotation': img.get('need_rotation', False)
                }
                px_data.append(px_img)

            # 计算统计信息
            stats = calculate_layout_statistics_px(px_data, container_config)

            # 生成文档
            success = generate_matplotlib_documentation(
                stats=stats,
                px_data=px_data,
                container_config=container_config,
                output_path=doc_path
            )

            if success and self.log_signal:
                self.log_signal.emit(f"matplotlib文档生成成功: {doc_path}")

            return success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"生成matplotlib文档失败: {str(e)}")
            log.error(f"生成matplotlib文档失败: {str(e)}")
            return False

    def create_matplotlib_test_with_cm_data(self, cm_data: List[Dict[str, Any]],
                                           output_path: str,
                                           base_width_cm: int = 200,
                                           horizontal_expansion_cm: int = 2,
                                           max_height_cm: int = 5000,
                                           cm_to_px_ratio: float = 1.0,
                                           title: str = "RectPack算法cm转px测试模式") -> bool:
        """
        使用cm数据创建matplotlib测试模式，自动转换为px单位

        Args:
            cm_data: cm单位的图片数据列表
            output_path: 输出文件路径
            base_width_cm: 基础宽度（cm）
            horizontal_expansion_cm: 水平拓展（cm）
            max_height_cm: 最大高度（cm）
            cm_to_px_ratio: cm到px的转换比例，默认1.0表示1cm=1px
            title: 图形标题

        Returns:
            bool: 是否创建成功
        """
        if not MATPLOTLIB_AVAILABLE:
            if self.log_signal:
                self.log_signal.emit("错误: matplotlib工具模块不可用，无法创建matplotlib测试模式")
            return False

        try:
            # 转换cm数据为px数据
            px_data = convert_cm_to_px_data(cm_data, cm_to_px_ratio)

            # 创建容器配置（px单位）
            container_config = create_container_config_with_expansion(
                base_width=int(base_width_cm * cm_to_px_ratio),
                horizontal_expansion=int(horizontal_expansion_cm * cm_to_px_ratio),
                max_height=int(max_height_cm * cm_to_px_ratio),
                spacing=int(self.image_spacing * cm_to_px_ratio)
            )

            # 创建matplotlib测试模式
            success = create_matplotlib_test_mode(
                px_data=px_data,
                container_config=container_config,
                output_path=output_path,
                title=title
            )

            if success and self.log_signal:
                self.log_signal.emit(f"matplotlib cm转px测试模式创建成功: {output_path}")
                self.log_signal.emit(f"转换比例: 1cm = {cm_to_px_ratio}px")

            return success

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"创建matplotlib cm转px测试模式失败: {str(e)}")
            log.error(f"创建matplotlib cm转px测试模式失败: {str(e)}")
            return False
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法matplotlib测试模式工具模块
分步骤、分阶段、模块化实现

第一阶段：数据准备模块
第二阶段：matplotlib绘制模块
第三阶段：集成优化模块
第四阶段：测试验证模块
"""

import logging
import time
import os
from typing import List, Tuple, Dict, Any, Optional
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 配置日志
log = logging.getLogger(__name__)

def setup_chinese_font_for_matplotlib():
    """
    设置matplotlib中文字体，解决中文乱码问题 - 增强版
    """
    try:
        import matplotlib.font_manager as fm
        import platform
        import os

        # 根据操作系统选择合适的中文字体
        system = platform.system()

        if system == 'Windows':
            # Windows系统中文字体路径和名称
            font_configs = [
                ('SimHei', ['C:/Windows/Fonts/simhei.ttf']),
                ('Microsoft YaHei', ['C:/Windows/Fonts/msyh.ttc', 'C:/Windows/Fonts/msyh.ttf']),
                ('SimSun', ['C:/Windows/Fonts/simsun.ttc', 'C:/Windows/Fonts/simsun.ttf']),
                ('KaiTi', ['C:/Windows/Fonts/simkai.ttf']),
                ('Microsoft JhengHei', ['C:/Windows/Fonts/msjh.ttc']),
            ]
        elif system == 'Darwin':  # macOS
            font_configs = [
                ('PingFang SC', ['/System/Library/Fonts/PingFang.ttc']),
                ('Heiti SC', ['/System/Library/Fonts/STHeiti Light.ttc']),
                ('STHeiti', ['/System/Library/Fonts/STHeiti Medium.ttc']),
                ('Arial Unicode MS', ['/Library/Fonts/Arial Unicode.ttf']),
            ]
        else:  # Linux
            font_configs = [
                ('WenQuanYi Micro Hei', ['/usr/share/fonts/truetype/wqy/wqy-microhei.ttc']),
                ('Droid Sans Fallback', ['/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf']),
                ('Noto Sans CJK SC', ['/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc']),
            ]

        # 尝试设置字体
        font_set = False
        working_font = None

        for font_name, font_paths in font_configs:
            # 检查字体文件是否存在
            font_exists = any(os.path.exists(path) for path in font_paths)

            if font_exists:
                try:
                    # 尝试设置字体
                    plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                    plt.rcParams['axes.unicode_minus'] = False

                    # 测试字体是否能正确显示中文
                    test_fig, test_ax = plt.subplots(figsize=(1, 1))
                    test_ax.text(0.5, 0.5, '测试', fontsize=12)
                    plt.close(test_fig)

                    font_set = True
                    working_font = font_name
                    log.info(f"matplotlib成功设置中文字体: {font_name}")
                    break

                except Exception as e:
                    log.warning(f"matplotlib字体 {font_name} 设置失败: {str(e)}")
                    continue

        if not font_set:
            # 如果都失败了，尝试系统默认字体
            try:
                # 获取系统中所有可用的中文字体
                available_fonts = [f.name for f in fm.fontManager.ttflist]
                chinese_fonts = [f for f in available_fonts if any(keyword in f for keyword in
                               ['SimHei', 'SimSun', 'Microsoft', 'YaHei', 'PingFang', 'Heiti', 'WenQuanYi', 'Noto'])]

                if chinese_fonts:
                    plt.rcParams['font.sans-serif'] = chinese_fonts[:3] + ['DejaVu Sans']
                    plt.rcParams['axes.unicode_minus'] = False
                    working_font = chinese_fonts[0]
                    font_set = True
                    log.info(f"matplotlib使用系统检测到的中文字体: {working_font}")
                else:
                    # 最后的备选方案
                    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                    plt.rcParams['axes.unicode_minus'] = False
                    log.warning("matplotlib警告: 未找到中文字体，使用默认字体，中文可能显示为方块")

            except Exception as e:
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                log.error(f"matplotlib字体检测失败: {str(e)}，使用默认字体")

        # 清除matplotlib字体缓存，确保新设置生效
        try:
            fm._rebuild()
        except:
            pass

        return font_set

    except Exception as e:
        log.error(f"matplotlib设置中文字体失败: {str(e)}")
        return False

# 初始化中文字体支持
setup_chinese_font_for_matplotlib()

# ============================================================================
# 第一阶段：数据准备模块
# ============================================================================

def convert_cm_to_px_data(cm_data: List[Dict[str, Any]],
                         cm_to_px_ratio: float = 1.0) -> List[Dict[str, Any]]:
    """
    将cm单位的数据转换为px单位的数据

    Args:
        cm_data: cm单位的图片数据列表
        cm_to_px_ratio: cm到px的转换比例，默认1.0表示1cm=1px

    Returns:
        List[Dict[str, Any]]: px单位的图片数据列表
    """
    px_data = []

    for i, img in enumerate(cm_data):
        try:
            # 提取cm尺寸
            width_cm = img.get('width', 0)
            height_cm = img.get('height', 0)

            # 转换为px尺寸
            width_px = int(width_cm * cm_to_px_ratio)
            height_px = int(height_cm * cm_to_px_ratio)

            # 创建px数据对象
            px_img = {
                'id': img.get('id', i + 1),
                'name': img.get('name', f'Image_{i+1}'),
                'width': width_px,
                'height': height_px,
                'x': img.get('x', 0),
                'y': img.get('y', 0),
                'image_class': img.get('image_class', 'C'),
                'rotated': img.get('rotated', False),
                'need_rotation': img.get('need_rotation', False),
                'original_width_cm': width_cm,
                'original_height_cm': height_cm,
                'cm_to_px_ratio': cm_to_px_ratio
            }

            px_data.append(px_img)

        except Exception as e:
            log.error(f"转换第{i+1}张图片数据失败: {str(e)}")
            continue

    log.info(f"数据转换完成: {len(cm_data)} -> {len(px_data)} 张图片 (比例: {cm_to_px_ratio})")
    return px_data


def create_container_config_with_expansion(base_width: int,
                                         horizontal_expansion: int = 0,
                                         max_height: int = 5000,
                                         spacing: int = 1) -> Dict[str, int]:
    """
    创建包含水平拓展的容器配置

    Args:
        base_width: 基础宽度 (px)
        horizontal_expansion: 水平拓展 (px)
        max_height: 最大高度 (px)
        spacing: 图片间距 (px)

    Returns:
        Dict[str, int]: 容器配置
    """
    actual_width = base_width + horizontal_expansion

    config = {
        'base_width': base_width,
        'horizontal_expansion': horizontal_expansion,
        'actual_width': actual_width,
        'max_height': max_height,
        'spacing': spacing,
        'total_area': actual_width * max_height
    }

    log.info(f"容器配置: 基础宽度{base_width}px + 拓展{horizontal_expansion}px = 实际宽度{actual_width}px, "
             f"最大高度{max_height}px, 间距{spacing}px")

    return config


def validate_px_data(px_data: List[Dict[str, Any]],
                    container_config: Dict[str, int]) -> bool:
    """
    验证px数据的有效性

    Args:
        px_data: px单位的图片数据
        container_config: 容器配置

    Returns:
        bool: 数据是否有效
    """
    if not px_data:
        log.error("图片数据为空")
        return False

    container_width = container_config.get('actual_width', 0)
    container_height = container_config.get('max_height', 0)

    for i, img in enumerate(px_data):
        # 检查基本字段
        required_fields = ['width', 'height', 'x', 'y']
        for field in required_fields:
            if field not in img:
                log.error(f"第{i+1}张图片缺少字段: {field}")
                return False

        # 检查尺寸有效性
        if img['width'] <= 0 or img['height'] <= 0:
            log.error(f"第{i+1}张图片尺寸无效: {img['width']}x{img['height']}")
            return False

        # 检查位置有效性
        if img['x'] < 0 or img['y'] < 0:
            log.error(f"第{i+1}张图片位置无效: ({img['x']}, {img['y']})")
            return False

        # 检查是否超出容器边界
        if img['x'] + img['width'] > container_width:
            log.warning(f"第{i+1}张图片超出容器宽度: {img['x']} + {img['width']} > {container_width}")

        if img['y'] + img['height'] > container_height:
            log.warning(f"第{i+1}张图片超出容器高度: {img['y']} + {img['height']} > {container_height}")

    log.info(f"数据验证通过: {len(px_data)} 张图片")
    return True


def calculate_layout_statistics_px(px_data: List[Dict[str, Any]],
                                  container_config: Dict[str, int]) -> Dict[str, Any]:
    """
    计算px数据的布局统计信息

    Args:
        px_data: px单位的图片数据
        container_config: 容器配置

    Returns:
        Dict[str, Any]: 统计信息
    """
    if not px_data:
        return {
            'placed_count': 0,
            'total_area_px': 0,
            'used_area_px': 0,
            'container_width_px': container_config.get('actual_width', 0),
            'container_height_px': 0,
            'container_area_px': 0,
            'utilization_rate': 0.0,
            'rotated_count': 0,
            'average_area_px': 0
        }

    # 计算实际使用的画布高度
    max_y = max(img['y'] + img['height'] for img in px_data)
    container_width = container_config['actual_width']
    container_height = min(max_y, container_config['max_height'])

    # 计算面积
    used_area = sum(img['width'] * img['height'] for img in px_data)
    container_area = container_width * container_height

    # 计算利用率
    utilization_rate = (used_area / container_area * 100) if container_area > 0 else 0

    # 计算旋转图片数量
    rotated_count = sum(1 for img in px_data if img.get('rotated', False) or img.get('need_rotation', False))

    # 计算平均面积
    average_area = used_area / len(px_data) if px_data else 0

    stats = {
        'placed_count': len(px_data),
        'total_area_px': sum(img['width'] * img['height'] for img in px_data),
        'used_area_px': used_area,
        'container_width_px': container_width,
        'container_height_px': container_height,
        'container_area_px': container_area,
        'utilization_rate': utilization_rate,
        'rotated_count': rotated_count,
        'average_area_px': average_area,
        'base_width_px': container_config['base_width'],
        'horizontal_expansion_px': container_config['horizontal_expansion'],
        'spacing_px': container_config['spacing']
    }

    log.info(f"统计计算完成: {stats['placed_count']} 张图片, 利用率 {utilization_rate:.2f}%")
    return stats


# ============================================================================
# 第二阶段：matplotlib绘制模块
# ============================================================================

def create_matplotlib_canvas(container_config: Dict[str, int],
                            title: str = "RectPack算法布局结果") -> Tuple[plt.Figure, plt.Axes]:
    """
    创建matplotlib画布

    Args:
        container_config: 容器配置
        title: 图形标题

    Returns:
        Tuple[plt.Figure, plt.Axes]: 图形和坐标轴对象
    """
    try:
        # 计算图形尺寸
        canvas_width = container_config['actual_width']
        canvas_height = container_config['max_height']

        # 动态调整图形尺寸，保持合理的宽高比
        aspect_ratio = canvas_height / canvas_width
        fig_width = 12
        fig_height = max(6, min(16, fig_width * aspect_ratio))

        # 创建图形
        fig, ax = plt.subplots(figsize=(fig_width, fig_height))

        # 设置坐标轴
        ax.set_xlim(0, canvas_width)
        ax.set_ylim(0, canvas_height)
        ax.set_aspect('equal')

        # 绘制容器边界
        container_rect = plt.Rectangle((0, 0), canvas_width, canvas_height,
                                     fill=False, edgecolor='black', linewidth=2)
        ax.add_patch(container_rect)

        # 如果有水平拓展，绘制基础宽度线
        if container_config['horizontal_expansion'] > 0:
            base_width = container_config['base_width']
            ax.axvline(x=base_width, color='red', linestyle='--', linewidth=1, alpha=0.7)
            ax.text(base_width + 5, canvas_height * 0.95,
                   f'基础宽度: {base_width}px',
                   rotation=90, va='top', ha='left',
                   color='red', fontsize=8)

        # 设置标题
        ax.set_title(title, fontsize=14, fontweight='bold', pad=20)

        # 设置坐标轴标签
        ax.set_xlabel('宽度 (px)', fontsize=10)
        ax.set_ylabel('高度 (px)', fontsize=10)

        # 添加网格
        ax.grid(True, linestyle='--', alpha=0.3)

        log.info(f"matplotlib画布创建成功: {canvas_width}x{canvas_height}px")
        return fig, ax

    except Exception as e:
        log.error(f"创建matplotlib画布失败: {str(e)}")
        raise


def get_matplotlib_colors(num_colors: int) -> List[Tuple[float, float, float]]:
    """
    获取matplotlib颜色列表，确保相邻颜色不同

    Args:
        num_colors: 需要的颜色数量

    Returns:
        List[Tuple[float, float, float]]: 颜色列表
    """
    # 使用tab20颜色方案，提供20种不同颜色
    if num_colors <= 20:
        colors = plt.cm.tab20(np.linspace(0, 1, 20))
    else:
        # 如果需要更多颜色，使用tab20c和tab20b组合
        colors1 = plt.cm.tab20(np.linspace(0, 1, 20))
        colors2 = plt.cm.tab20b(np.linspace(0, 1, 20))
        colors3 = plt.cm.tab20c(np.linspace(0, 1, 20))
        colors = np.concatenate([colors1, colors2, colors3])

    return colors[:num_colors]


def draw_image_rectangles(ax: plt.Axes,
                         px_data: List[Dict[str, Any]],
                         colors: List[Tuple[float, float, float]]) -> None:
    """
    绘制图片矩形

    Args:
        ax: matplotlib坐标轴
        px_data: px单位的图片数据
        colors: 颜色列表
    """
    try:
        for i, img in enumerate(px_data):
            # 选择颜色（确保相邻图片颜色不同）
            color = colors[i % len(colors)]

            # 创建矩形
            rect = plt.Rectangle(
                (img['x'], img['y']),
                img['width'],
                img['height'],
                fill=True,
                facecolor=color,
                edgecolor='black',
                linewidth=0.5,
                alpha=0.7
            )
            ax.add_patch(rect)

        log.info(f"绘制完成: {len(px_data)} 个图片矩形")

    except Exception as e:
        log.error(f"绘制图片矩形失败: {str(e)}")
        raise


def add_image_labels(ax: plt.Axes, px_data: List[Dict[str, Any]]) -> None:
    """
    添加图片标签

    Args:
        ax: matplotlib坐标轴
        px_data: px单位的图片数据
    """
    try:
        for img in px_data:
            # 计算标签位置（矩形中心）
            center_x = img['x'] + img['width'] / 2
            center_y = img['y'] + img['height'] / 2

            # 动态调整字体大小
            font_size = max(6, min(10, min(img['width'], img['height']) / 15))

            # 创建标签文本
            label_parts = []

            # 添加ID或名称
            if 'name' in img and img['name']:
                label_parts.append(str(img['name']))
            else:
                label_parts.append(str(img.get('id', '?')))

            # 添加尺寸信息
            label_parts.append(f"{img['width']}x{img['height']}")

            # 添加旋转标记
            if img.get('rotated', False) or img.get('need_rotation', False):
                label_parts.append("R")

            label_text = "\n".join(label_parts)

            # 绘制标签
            ax.text(center_x, center_y, label_text,
                   ha='center', va='center',
                   fontsize=font_size,
                   color='white',
                   weight='bold',
                   bbox=dict(boxstyle='round,pad=0.2',
                            facecolor='black',
                            alpha=0.6))

        log.info(f"添加标签完成: {len(px_data)} 个图片标签")

    except Exception as e:
        log.error(f"添加图片标签失败: {str(e)}")
        # 标签添加失败不影响主要功能
        pass


def add_statistics_display(ax: plt.Axes,
                          stats: Dict[str, Any],
                          container_config: Dict[str, int]) -> None:
    """
    添加统计信息显示 - 修复中文乱码问题

    Args:
        ax: matplotlib坐标轴
        stats: 统计信息
        container_config: 容器配置
    """
    try:
        # 确保中文字体设置正确
        setup_chinese_font_for_matplotlib()

        # 准备统计文本 - 使用更清晰的中文标签
        stats_lines = [
            f"画布: {stats['placed_count']} 张",
            f"宽度: {stats['container_width_px']}x{stats['container_height_px']} px",
            f"高度: {stats['base_width_px']} px",
            f"间距: {stats['spacing_px']} px",
            f"利用率: {stats['utilization_rate']:.2f}%",
            f"数量: {stats['rotated_count']} 张"
        ]

        # 计算文本位置
        text_x = stats['container_width_px'] * 0.02
        text_y = stats['container_height_px'] * 0.98
        text_content = "\n".join(stats_lines)

        # 绘制统计信息 - 移除monospace字体，使用中文字体
        ax.text(text_x, text_y, text_content,
               verticalalignment='top',
               horizontalalignment='left',
               bbox=dict(boxstyle='round,pad=0.5',
                        facecolor='white',
                        alpha=0.9,
                        edgecolor='gray'),
               fontsize=10,
               weight='normal')  # 移除fontfamily='monospace'，使用默认中文字体

        log.info("统计信息显示添加完成 - 中文字体已修复")

    except Exception as e:
        log.error(f"添加统计信息显示失败: {str(e)}")
        # 统计信息显示失败不影响主要功能
        pass


def save_matplotlib_result(fig: plt.Figure, output_path: str, dpi: int = 150) -> bool:
    """
    保存matplotlib结果

    Args:
        fig: matplotlib图形对象
        output_path: 输出文件路径
        dpi: 图片分辨率

    Returns:
        bool: 是否保存成功
    """
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 保存图片
        fig.savefig(output_path, dpi=dpi, bbox_inches='tight',
                   facecolor='white', edgecolor='none')

        log.info(f"matplotlib结果已保存: {output_path}")
        return True

    except Exception as e:
        log.error(f"保存matplotlib结果失败: {str(e)}")
        return False


def close_matplotlib_figure(fig: plt.Figure) -> None:
    """
    关闭matplotlib图形，释放内存

    Args:
        fig: matplotlib图形对象
    """
    try:
        plt.close(fig)
        log.debug("matplotlib图形已关闭")
    except Exception as e:
        log.error(f"关闭matplotlib图形失败: {str(e)}")


# ============================================================================
# 第三阶段：集成优化模块
# ============================================================================

def create_matplotlib_test_mode(px_data: List[Dict[str, Any]],
                               container_config: Dict[str, int],
                               output_path: str,
                               title: str = "RectPack算法测试模式") -> bool:
    """
    创建完整的matplotlib测试模式

    Args:
        px_data: px单位的图片数据
        container_config: 容器配置
        output_path: 输出文件路径
        title: 图形标题

    Returns:
        bool: 是否创建成功
    """
    fig = None
    try:
        # 验证数据
        if not validate_px_data(px_data, container_config):
            log.error("数据验证失败")
            return False

        # 计算统计信息
        stats = calculate_layout_statistics_px(px_data, container_config)

        # 创建画布
        fig, ax = create_matplotlib_canvas(container_config, title)

        # 获取颜色
        colors = get_matplotlib_colors(len(px_data))

        # 绘制图片矩形
        draw_image_rectangles(ax, px_data, colors)

        # 添加图片标签
        add_image_labels(ax, px_data)

        # 添加统计信息
        add_statistics_display(ax, stats, container_config)

        # 保存结果
        success = save_matplotlib_result(fig, output_path)

        if success:
            log.info(f"matplotlib测试模式创建成功: {output_path}")

        return success

    except Exception as e:
        log.error(f"创建matplotlib测试模式失败: {str(e)}")
        return False

    finally:
        # 确保释放资源
        if fig is not None:
            close_matplotlib_figure(fig)


def generate_matplotlib_documentation(stats: Dict[str, Any],
                                     px_data: List[Dict[str, Any]],
                                     container_config: Dict[str, int],
                                     output_path: str) -> bool:
    """
    生成matplotlib模式的详细文档

    Args:
        stats: 统计信息
        px_data: px单位的图片数据
        container_config: 容器配置
        output_path: 输出文件路径

    Returns:
        bool: 是否生成成功
    """
    try:
        import time

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 生成文档内容
        content = []
        content.append("# RectPack算法matplotlib测试模式报告")
        content.append("")
        content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("")

        # 容器详情
        content.append("★ 容器详情")
        content.append("")
        content.append(f"- 基础宽度: {container_config['base_width']}px")
        content.append(f"- 水平拓展: {container_config['horizontal_expansion']}px")
        content.append(f"- 实际宽度: {container_config['actual_width']}px")
        content.append(f"- 最大高度: {container_config['max_height']}px")
        content.append(f"- 图片间距: {container_config['spacing']}px")
        content.append(f"- 总面积: {container_config['total_area']:,}px²")
        content.append("")

        # 算法信息
        content.append("★ 算法信息")
        content.append("")
        content.append("- 绘制引擎: matplotlib")
        content.append("- 单位系统: 统一px单位")
        content.append("- 颜色方案: tab20 (20种颜色循环)")
        content.append("- 旋转支持: 启用")
        content.append("- 标签显示: 启用")
        content.append("")

        # 布局统计
        content.append("★ 布局统计")
        content.append("")
        content.append(f"- 成功放置图片: {stats['placed_count']}张")
        content.append(f"- 实际画布高度: {stats['container_height_px']}px")
        content.append(f"- 已用面积: {stats['used_area_px']:,}px²")
        content.append(f"- 画布面积: {stats['container_area_px']:,}px²")
        content.append(f"- 利用率: {stats['utilization_rate']:.2f}%")
        content.append(f"- 旋转图片数: {stats['rotated_count']}张")
        content.append(f"- 平均图片面积: {stats['average_area_px']:.0f}px²")
        content.append("")

        # 利用率评价
        utilization = stats['utilization_rate']
        if utilization >= 85:
            rating = "★★★★★ 优秀"
        elif utilization >= 75:
            rating = "★★★★☆ 良好"
        elif utilization >= 65:
            rating = "★★★☆☆ 中等"
        elif utilization >= 50:
            rating = "★★☆☆☆ 较差"
        else:
            rating = "★☆☆☆☆ 待优化"

        content.append(f"- 利用率评价: {rating}")
        content.append("")

        # 详细图片信息
        content.append("★ 详细图片信息")
        content.append("")
        content.append("| 序号 | 图片名称 | 尺寸(px) | 位置(x,y) | 旋转 | 面积(px²) |")
        content.append("|------|----------|----------|-----------|------|-----------|")

        for i, img in enumerate(px_data, 1):
            name = img.get('name', f'Image_{i}')
            width = img['width']
            height = img['height']
            x = img['x']
            y = img['y']
            rotated = '是' if (img.get('rotated', False) or img.get('need_rotation', False)) else '否'
            area = width * height

            content.append(f"| {i} | {name} | {width}x{height} | ({x},{y}) | {rotated} | {area:,} |")

        content.append("")

        # 技术说明
        content.append("★ 技术说明")
        content.append("")
        content.append("本报告使用matplotlib绘制引擎生成，具有以下特点：")
        content.append("1. **统一px单位**: 避免cm到px转换的复杂性")
        content.append("2. **水平拓展支持**: 容器宽度 = 基础宽度 + 水平拓展")
        content.append("3. **20种颜色循环**: 确保相邻图片颜色不同")
        content.append("4. **智能标签**: 显示图片ID、尺寸和旋转状态")
        content.append("5. **详细统计**: 完整的布局和性能统计信息")
        content.append("")

        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))

        log.info(f"matplotlib文档已生成: {output_path}")
        return True

    except Exception as e:
        log.error(f"生成matplotlib文档失败: {str(e)}")
        return False

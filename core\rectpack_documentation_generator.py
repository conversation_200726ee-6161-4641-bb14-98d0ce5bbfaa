#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack算法详细说明文档生成器

生成详细的生产环境说明文档，包含完整的图片排列信息

作者: PS画布修复团队
日期: 2024-12-19
版本: 详细文档生成
"""

import time
from typing import Dict, Any, List
import os


class RectPackDocumentationGenerator:
    """RectPack算法说明文档生成器"""

    def __init__(self):
        self.generation_time = time.strftime('%Y-%m-%d %H:%M:%S')

    def generate_production_documentation(self,
                                        canvas_info: Dict[str, Any],
                                        images_info: List[Dict[str, Any]],
                                        output_path: str) -> bool:
        """
        生成生产环境详细说明文档

        Args:
            canvas_info: 画布信息
            images_info: 图片信息列表
            output_path: 输出路径

        Returns:
            bool: 是否生成成功
        """
        try:
            # 提取基本信息
            material_name = canvas_info.get('material_name', '未知材质')
            canvas_sequence = canvas_info.get('canvas_sequence', 1)
            canvas_width_px = canvas_info.get('canvas_width_px', 0)
            canvas_height_px = canvas_info.get('canvas_height', 0)
            ppi = canvas_info.get('ppi', 72)

            # 单位转换
            canvas_width_cm = canvas_width_px / (ppi / 2.54)
            canvas_height_cm = canvas_height_px / (ppi / 2.54)
            canvas_width_m = canvas_width_cm / 100

            # 计算统计信息
            stats = self._calculate_statistics(images_info, canvas_width_px, canvas_height_px)

            # 生成文档内容
            content = self._generate_document_content(
                material_name=material_name,
                canvas_sequence=canvas_sequence,
                canvas_width_m=canvas_width_m,
                canvas_width_cm=canvas_width_cm,
                canvas_height_cm=canvas_height_cm,
                canvas_height_px=canvas_height_px,
                horizontal_expansion_cm=canvas_info.get('horizontal_expansion_cm', 0),
                max_height_cm=canvas_info.get('max_height_cm', canvas_height_cm),
                unit_conversion=canvas_info.get('unit_conversion', 'cm_to_px_direct'),
                test_all_data=canvas_info.get('test_all_data', False),
                stats=stats,
                images_info=images_info,
                ppi=ppi
            )

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return True

        except Exception as e:
            print(f"生成说明文档失败: {str(e)}")
            return False

    def _calculate_statistics(self, images_info: List[Dict[str, Any]],
                            canvas_width_px: int, canvas_height_px: int) -> Dict[str, Any]:
        """计算统计信息"""
        if not images_info:
            return {
                'utilization_rate': 0.0,
                'rotation_rate': 0.0,
                'total_images': 0,
                'rotated_images': 0
            }

        # 计算利用率
        total_canvas_area = canvas_width_px * canvas_height_px
        used_area = 0
        rotated_count = 0

        for img in images_info:
            img_width = img.get('width', 0)
            img_height = img.get('height', 0)
            used_area += img_width * img_height

            if img.get('rotated', False) or img.get('need_rotation', False):
                rotated_count += 1

        utilization_rate = (used_area / total_canvas_area * 100) if total_canvas_area > 0 else 0
        rotation_rate = (rotated_count / len(images_info) * 100) if images_info else 0

        return {
            'utilization_rate': utilization_rate,
            'rotation_rate': rotation_rate,
            'total_images': len(images_info),
            'rotated_images': rotated_count
        }

    def _generate_document_content(self,
                                 material_name: str,
                                 canvas_sequence: int,
                                 canvas_width_m: float,
                                 canvas_width_cm: float,
                                 canvas_height_cm: float,
                                 canvas_height_px: int,
                                 horizontal_expansion_cm: float,
                                 max_height_cm: float,
                                 unit_conversion: str,
                                 test_all_data: bool,
                                 stats: Dict[str, Any],
                                 images_info: List[Dict[str, Any]],
                                 ppi: int) -> str:
        """生成文档内容"""

        lines = []

        # 文档标题
        lines.append(f"# {material_name}-{canvas_sequence} 生产环境说明文档")
        lines.append("")

        # 基本信息
        lines.append(f"生成时间: {self.generation_time}")
        lines.append(f"材质名称: {material_name}")
        lines.append(f"画布序号: {canvas_sequence}")
        lines.append(f"画布宽度: {canvas_width_m:.2f}米 ({canvas_width_cm:.0f}厘米)")
        lines.append(f"画布高度: {canvas_height_cm:.2f} 厘米 ({canvas_height_px} 像素)")
        lines.append(f"水平拓展: {horizontal_expansion_cm:.1f} 厘米")
        lines.append(f"最大高度限制: {max_height_cm:.1f} 厘米")
        lines.append(f"单位转换方式: {unit_conversion}")
        lines.append(f"测试全部数据: {'是' if test_all_data else '否'}")
        lines.append("")

        # 利用率统计
        lines.append("## 利用率统计")
        lines.append(f"画布利用率: {stats['utilization_rate']:.2f}%")
        lines.append(f"旋转图片比例: {stats['rotation_rate']:.2f}% ({stats['rotated_images']}/{stats['total_images']})")
        lines.append("")

        # 图片统计
        lines.append("## 图片统计")
        lines.append(f"总图片数: {stats['total_images']}")
        lines.append("RectPack算法排列图片")
        lines.append("")

        # 图片排列信息
        lines.append("## 图片排列信息")
        lines.append("序号   名称                            分类   位置(x,y)        尺寸(宽x高)(px)        表格宽-高(cm)       旋转")

        for i, img in enumerate(images_info, 1):
            # 提取图片信息
            name = str(img.get('name', f'Image_{i}')).ljust(30)[:30]
            image_class = img.get('image_class', 'C')
            x = img.get('x', 0)
            y = img.get('y', 0)
            width_px = img.get('width', 0)
            height_px = img.get('height', 0)
            rotated = img.get('rotated', False) or img.get('need_rotation', False)

            # 转换为厘米（表格尺寸）
            width_cm = width_px / (ppi / 2.54)
            height_cm = height_px / (ppi / 2.54)

            # 格式化输出
            position_str = f"({x},{y})".ljust(16)
            size_px_str = f"({width_px}x{height_px})".ljust(18)
            size_cm_str = f"{width_cm:.0f}-{height_cm:.0f}".ljust(15)
            rotation_str = "是" if rotated else "否"

            line = f"{i:<4} {name} {image_class:<4} {position_str} {size_px_str} {size_cm_str} {rotation_str}"
            lines.append(line)

        return '\n'.join(lines)


def create_rectpack_documentation(canvas_info: Dict[str, Any],
                                images_info: List[Dict[str, Any]],
                                output_path: str) -> bool:
    """
    创建RectPack算法说明文档的便捷函数

    Args:
        canvas_info: 画布信息
        images_info: 图片信息列表
        output_path: 输出路径

    Returns:
        bool: 是否生成成功
    """
    generator = RectPackDocumentationGenerator()
    return generator.generate_production_documentation(canvas_info, images_info, output_path)


# 导出主要类和函数
__all__ = [
    'RectPackDocumentationGenerator',
    'create_rectpack_documentation'
]

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
迭代卡片对话框模块

提供画布迭代卡片的显示和选择功能：
1. 显示多个迭代卡片，按利用率排序
2. 支持选择最佳卡片
3. 支持查看所有卡片
4. 支持缩放和平移卡片
"""

import os
import sys
import logging
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
import tempfile

from PyQt6.QtCore import Qt, QSize, QRect, QPoint, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QPainter, QColor, QPen, QFont, QImage
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
    QLabel, QPushButton, QScrollArea, QWidget, 
    QFrame, QSizePolicy, QSpacerItem, QMessageBox
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("IterationCardsDialog")

class CardWidget(QFrame):
    """迭代卡片小部件"""
    
    clicked = pyqtSignal(int)  # 点击信号，传递卡片索引
    
    def __init__(self, card: Dict[str, Any], index: int, parent=None):
        """
        初始化卡片小部件
        
        Args:
            card: 卡片数据
            index: 卡片索引
            parent: 父窗口
        """
        super().__init__(parent)
        self.card = card
        self.index = index
        self.selected = False
        
        # 设置样式
        self.setFrameShape(QFrame.Shape.Box)
        self.setFrameShadow(QFrame.Shadow.Raised)
        self.setLineWidth(2)
        self.setMidLineWidth(1)
        self.setMinimumSize(300, 300)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 创建卡片图像
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setMinimumSize(280, 200)
        self.image_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        layout.addWidget(self.image_label)
        
        # 创建信息标签
        self.info_label = QLabel()
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.info_label.setWordWrap(True)
        layout.addWidget(self.info_label)
        
        # 更新UI
        self.update_ui()
    
    def update_ui(self):
        """更新UI显示"""
        # 生成卡片图像
        self.generate_card_image()
        
        # 更新信息标签
        params = self.card['params']
        utilization = self.card['canvas_utilization']
        success_rate = self.card['success_rate']
        
        info_text = (
            f"<b>利用率: {utilization:.4f}%</b><br>"
            f"成功率: {success_rate:.2f}%<br>"
            f"水平优先级: {params['horizontal_priority']}%<br>"
            f"空隙填充: {params['gap_filling_priority']}%<br>"
            f"旋转: {params['rotation_priority']}%"
        )
        
        self.info_label.setText(info_text)
        
        # 更新选中状态
        self.update_selection()
    
    def generate_card_image(self):
        """生成卡片图像"""
        try:
            # 获取卡片信息
            placed_images = self.card['placed_images']
            canvas_width = self.card['canvas_width']
            max_height = self.card['max_height']
            
            # 计算缩放比例
            scale = min(280 / canvas_width, 200 / max_height)
            
            # 创建图像
            image = QImage(int(canvas_width * scale), int(max_height * scale), QImage.Format.Format_RGB32)
            image.fill(Qt.GlobalColor.white)
            
            # 创建绘图器
            painter = QPainter(image)
            
            # 绘制画布边界
            painter.setPen(QPen(Qt.GlobalColor.black, 2))
            painter.drawRect(0, 0, int(canvas_width * scale), int(max_height * scale))
            
            # 绘制已放置的图片
            for img in placed_images:
                x = int(img['x'] * scale)
                y = int(img['y'] * scale)
                width = int(img['width'] * scale)
                height = int(img['height'] * scale)
                image_class = img.get('image_class', 'C')
                
                # 根据图片类别选择颜色
                if image_class == 'A':
                    color = QColor(255, 200, 200)  # 浅红色
                    outline = QColor(255, 0, 0)    # 红色
                elif image_class == 'B':
                    color = QColor(200, 200, 255)  # 浅蓝色
                    outline = QColor(0, 0, 255)    # 蓝色
                else:  # C类
                    color = QColor(200, 255, 200)  # 浅绿色
                    outline = QColor(0, 128, 0)    # 绿色
                
                # 绘制矩形
                painter.setBrush(color)
                painter.setPen(QPen(outline, 1))
                painter.drawRect(x, y, width, height)
            
            # 结束绘图
            painter.end()
            
            # 设置图像
            pixmap = QPixmap.fromImage(image)
            self.image_label.setPixmap(pixmap)
            
        except Exception as e:
            log.error(f"生成卡片图像失败: {str(e)}")
            self.image_label.setText("图像生成失败")
    
    def update_selection(self):
        """更新选中状态"""
        if self.selected:
            self.setStyleSheet("CardWidget { background-color: #e0f0ff; border: 2px solid #0078d7; }")
        else:
            self.setStyleSheet("")
    
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        self.update_selection()
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        self.clicked.emit(self.index)
        super().mousePressEvent(event)

class IterationCardsDialog(QDialog):
    """迭代卡片对话框"""
    
    card_selected = pyqtSignal(dict)  # 卡片选择信号，传递选中的卡片数据
    
    def __init__(self, cards: List[Dict[str, Any]], parent=None):
        """
        初始化迭代卡片对话框
        
        Args:
            cards: 迭代卡片列表
            parent: 父窗口
        """
        super().__init__(parent)
        self.cards = cards
        self.selected_index = -1
        self.card_widgets = []
        self.show_all_cards = False
        
        # 设置窗口属性
        self.setWindowTitle("画布迭代卡片")
        self.resize(1000, 600)
        
        # 创建UI
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建标题标签
        title_label = QLabel("请选择最佳的画布布局")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        main_layout.addWidget(title_label)
        
        # 创建说明标签
        desc_label = QLabel("以下是根据不同参数生成的画布布局，按利用率从高到低排序。请选择一个最适合的布局。")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setWordWrap(True)
        main_layout.addWidget(desc_label)
        
        # 创建卡片容器
        self.cards_container = QWidget()
        self.cards_layout = QGridLayout(self.cards_container)
        self.cards_layout.setSpacing(10)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.cards_container)
        main_layout.addWidget(scroll_area)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 创建查看所有卡片按钮
        self.view_all_button = QPushButton("查看所有卡片")
        self.view_all_button.clicked.connect(self.toggle_view_all_cards)
        button_layout.addWidget(self.view_all_button)
        
        # 添加弹性空间
        button_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        
        # 创建取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        # 创建确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.setEnabled(False)
        self.ok_button.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_button)
        
        # 添加按钮布局
        main_layout.addLayout(button_layout)
        
        # 显示卡片
        self.display_cards()
    
    def display_cards(self):
        """显示卡片"""
        # 清空卡片容器
        self.clear_cards()
        
        # 获取要显示的卡片
        display_cards = self.cards if self.show_all_cards else self.cards[:min(5, len(self.cards))]
        
        # 计算行数和列数
        if self.show_all_cards:
            # 垂直布局，每行1个卡片
            rows = len(display_cards)
            cols = 1
        else:
            # 水平布局，最多5个卡片一行
            rows = 1
            cols = min(5, len(display_cards))
        
        # 创建卡片小部件
        for i, card in enumerate(display_cards):
            # 计算行列位置
            if self.show_all_cards:
                row = i
                col = 0
            else:
                row = 0
                col = i
            
            # 创建卡片小部件
            card_widget = CardWidget(card, i, self)
            card_widget.clicked.connect(self.on_card_clicked)
            
            # 添加到布局
            self.cards_layout.addWidget(card_widget, row, col)
            self.card_widgets.append(card_widget)
        
        # 更新按钮文本
        self.view_all_button.setText("返回主视图" if self.show_all_cards else "查看所有卡片")
    
    def clear_cards(self):
        """清空卡片容器"""
        # 移除所有卡片小部件
        for widget in self.card_widgets:
            self.cards_layout.removeWidget(widget)
            widget.deleteLater()
        
        # 清空卡片小部件列表
        self.card_widgets = []
    
    def on_card_clicked(self, index):
        """卡片点击事件"""
        # 更新选中状态
        for i, widget in enumerate(self.card_widgets):
            widget.set_selected(i == index)
        
        # 更新选中索引
        self.selected_index = index
        
        # 启用确定按钮
        self.ok_button.setEnabled(True)
    
    def toggle_view_all_cards(self):
        """切换查看所有卡片"""
        self.show_all_cards = not self.show_all_cards
        self.display_cards()
    
    def accept(self):
        """确定按钮点击事件"""
        if self.selected_index >= 0 and self.selected_index < len(self.card_widgets):
            # 获取选中的卡片数据
            card_widget = self.card_widgets[self.selected_index]
            selected_card = card_widget.card
            
            # 发送卡片选择信号
            self.card_selected.emit(selected_card)
            
            # 关闭对话框
            super().accept()
        else:
            QMessageBox.warning(self, "警告", "请先选择一个卡片")
    
    def get_selected_card(self) -> Optional[Dict[str, Any]]:
        """获取选中的卡片"""
        if self.selected_index >= 0 and self.selected_index < len(self.cards):
            return self.cards[self.selected_index]
        return None

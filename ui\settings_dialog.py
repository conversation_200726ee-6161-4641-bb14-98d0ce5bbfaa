#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置对话框模块

提供应用设置界面：
1. 画布设置
2. Photoshop设置
3. 其他偏好设置
"""

import os
import logging
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QSpinBox, QDoubleSpinBox, QCheckBox,
    QComboBox, QDialogButtonBox, QTabWidget,
    QWidget, QFormLayout, QGroupBox, QFrame
)
from PyQt6.QtCore import Qt

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("SettingsDialog")

class SettingsDialog(QDialog):
    """设置对话框类"""

    def __init__(self, config_manager, parent=None):
        """初始化设置对话框

        Args:
            config_manager: 配置管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        self.config_manager = config_manager
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle("设置")
        self.setMinimumWidth(400)

        # 创建主布局
        layout = QVBoxLayout(self)

        # 创建选项卡
        tab_widget = QTabWidget()

        # 1. 基础设置选项卡 - 包含画布设置和基本配置
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)

        # 画布设置组
        canvas_group = QGroupBox("画布设置")
        canvas_layout = QFormLayout(canvas_group)
        self._init_canvas_ui(canvas_layout)
        basic_layout.addWidget(canvas_group)

        # 数据处理设置组
        data_processing_group = QGroupBox("数据处理设置")
        data_processing_layout = QFormLayout(data_processing_group)

        # 精确查询图案全称开关
        self.exact_pattern_search = QCheckBox("精确查询图案全称")
        self.exact_pattern_search.setToolTip("开启后，仅精确查询'图案全称'字段，不查询'图案'字段")
        data_processing_layout.addRow(self.exact_pattern_search)

        # 标准表格模式开关
        self.is_standard_mode = QCheckBox("使用标准表格模式")
        self.is_standard_mode.setToolTip("开启后，使用标准表格模式处理Excel数据；关闭后，使用自定义表格模式")
        data_processing_layout.addRow(self.is_standard_mode)

        # 模糊查询开关
        self.is_fuzzy_query = QCheckBox("启用模糊查询")
        self.is_fuzzy_query.setToolTip("开启后，使用模糊查询匹配图案名称；关闭后，使用精确匹配")
        data_processing_layout.addRow(self.is_fuzzy_query)

        # 添加说明标签
        data_processing_description = QLabel(
            "• 精确查询：仅查询'图案全称'字段，提高查询精度\n"
            "• 标准表格模式：使用标准Excel表格结构处理数据\n"
            "• 模糊查询：支持部分匹配的图案名称查询"
        )
        data_processing_description.setWordWrap(True)
        data_processing_description.setStyleSheet("color: #666; font-size: 11px;")
        data_processing_layout.addRow(data_processing_description)

        basic_layout.addWidget(data_processing_group)

        tab_widget.addTab(basic_tab, "基础设置")

        # 2. RectPack算法选项卡 - 包含基础和高级算法设置
        rectpack_tab = QWidget()
        rectpack_layout = QFormLayout(rectpack_tab)

        # RectPack算法基础设置组
        rectpack_basic_group = QGroupBox("RectPack算法基础设置")
        rectpack_basic_layout = QFormLayout(rectpack_basic_group)

        # RectPack旋转设置
        self.rectpack_rotation_enabled = QCheckBox("启用图片旋转")
        self.rectpack_rotation_enabled.setToolTip("启用后，RectPack算法会自动旋转图片以获得更好的排列效果")
        rectpack_basic_layout.addRow(self.rectpack_rotation_enabled)

        # RectPack排序策略
        self.rectpack_sort_strategy = QComboBox()
        self.rectpack_sort_strategy.addItems([
            "按面积排序 (推荐)",
            "按周长排序",
            "按差值排序",
            "按短边排序",
            "按长边排序",
            "按比例排序"
        ])
        self.rectpack_sort_strategy.setToolTip("选择RectPack算法的排序策略")
        rectpack_basic_layout.addRow("排序策略:", self.rectpack_sort_strategy)

        # RectPack装箱算法
        self.rectpack_pack_algorithm = QComboBox()
        self.rectpack_pack_algorithm.addItems([
            "Bottom-Left Fill (推荐)",
            "Best Fit First",
            "Best Bin Fit"
        ])
        self.rectpack_pack_algorithm.setToolTip("选择RectPack算法的装箱策略")
        rectpack_basic_layout.addRow("装箱算法:", self.rectpack_pack_algorithm)

        rectpack_layout.addWidget(rectpack_basic_group)

        # RectPack算法高级设置组
        rectpack_advanced_group = QGroupBox("RectPack算法高级设置")
        rectpack_advanced_layout = QFormLayout(rectpack_advanced_group)

        # Bin选择策略
        self.rectpack_bin_selection_strategy = QComboBox()
        self.rectpack_bin_selection_strategy.addItems([
            "Best Short Side Fit (推荐)",
            "Best Long Side Fit",
            "Best Area Fit",
            "Bottom Left Rule",
            "Contact Point Rule"
        ])
        self.rectpack_bin_selection_strategy.setToolTip("选择Bin选择策略")
        rectpack_advanced_layout.addRow("Bin选择策略:", self.rectpack_bin_selection_strategy)

        # 分割启发式
        self.rectpack_split_heuristic = QComboBox()
        self.rectpack_split_heuristic.addItems([
            "Shorter Leftover Horizontal (推荐)",
            "Shorter Leftover Vertical",
            "Longer Leftover Horizontal",
            "Longer Leftover Vertical"
        ])
        self.rectpack_split_heuristic.setToolTip("选择分割启发式策略")
        rectpack_advanced_layout.addRow("分割启发式:", self.rectpack_split_heuristic)

        # 自由矩形选择
        self.rectpack_free_rect_choice = QComboBox()
        self.rectpack_free_rect_choice.addItems([
            "Best Short Side Fit (推荐)",
            "Best Long Side Fit",
            "Best Area Fit",
            "Worst Area Fit",
            "Worst Short Side Fit",
            "Worst Long Side Fit"
        ])
        self.rectpack_free_rect_choice.setToolTip("选择自由矩形选择策略")
        rectpack_advanced_layout.addRow("自由矩形选择:", self.rectpack_free_rect_choice)

        rectpack_layout.addWidget(rectpack_advanced_group)

        # RectPack算法优化设置组
        rectpack_optimization_group = QGroupBox("RectPack算法优化设置")
        rectpack_optimization_layout = QFormLayout(rectpack_optimization_group)

        # 启用优化
        self.rectpack_enable_optimization = QCheckBox("启用利用率优化")
        self.rectpack_enable_optimization.setToolTip("启用后，算法会尝试多种配置以获得最佳利用率")
        rectpack_optimization_layout.addRow(self.rectpack_enable_optimization)

        # 优化迭代次数
        self.rectpack_optimization_iterations = QSpinBox()
        self.rectpack_optimization_iterations.setMinimum(1)
        self.rectpack_optimization_iterations.setMaximum(20)
        self.rectpack_optimization_iterations.setSingleStep(1)
        self.rectpack_optimization_iterations.setToolTip("优化算法的迭代次数，越多越精确但耗时更长")
        rectpack_optimization_layout.addRow("优化迭代次数:", self.rectpack_optimization_iterations)

        # 最小利用率阈值
        self.rectpack_min_utilization_threshold = QDoubleSpinBox()
        self.rectpack_min_utilization_threshold.setMinimum(50.0)
        self.rectpack_min_utilization_threshold.setMaximum(99.0)
        self.rectpack_min_utilization_threshold.setSingleStep(1.0)
        self.rectpack_min_utilization_threshold.setDecimals(1)
        self.rectpack_min_utilization_threshold.setSuffix("%")
        self.rectpack_min_utilization_threshold.setToolTip("最小利用率阈值，低于此值会尝试优化")
        rectpack_optimization_layout.addRow("最小利用率阈值:", self.rectpack_min_utilization_threshold)

        # 旋转惩罚系数
        self.rectpack_rotation_penalty = QDoubleSpinBox()
        self.rectpack_rotation_penalty.setMinimum(0.0)
        self.rectpack_rotation_penalty.setMaximum(1.0)
        self.rectpack_rotation_penalty.setSingleStep(0.01)
        self.rectpack_rotation_penalty.setDecimals(2)
        self.rectpack_rotation_penalty.setToolTip("旋转惩罚系数，越高越不倾向于旋转图片")
        rectpack_optimization_layout.addRow("旋转惩罚系数:", self.rectpack_rotation_penalty)

        # 宽高比偏好
        self.rectpack_aspect_ratio_preference = QDoubleSpinBox()
        self.rectpack_aspect_ratio_preference.setMinimum(0.1)
        self.rectpack_aspect_ratio_preference.setMaximum(10.0)
        self.rectpack_aspect_ratio_preference.setSingleStep(0.1)
        self.rectpack_aspect_ratio_preference.setDecimals(1)
        self.rectpack_aspect_ratio_preference.setToolTip("宽高比偏好：1.0=无偏好，>1偏好横向，<1偏好纵向")
        rectpack_optimization_layout.addRow("宽高比偏好:", self.rectpack_aspect_ratio_preference)

        rectpack_layout.addWidget(rectpack_optimization_group)

        tab_widget.addTab(rectpack_tab, "RectPack算法")

        # 3. 性能和调试选项卡 - 包含性能优化和调试设置
        performance_tab = QWidget()
        performance_layout = QFormLayout(performance_tab)

        # RectPack性能参数组
        performance_group = QGroupBox("RectPack性能参数")
        performance_group_layout = QFormLayout(performance_group)

        # 最大处理时间
        self.rectpack_max_processing_time = QSpinBox()
        self.rectpack_max_processing_time.setMinimum(30)
        self.rectpack_max_processing_time.setMaximum(3600)
        self.rectpack_max_processing_time.setSingleStep(30)
        self.rectpack_max_processing_time.setSuffix(" 秒")
        self.rectpack_max_processing_time.setToolTip("算法最大处理时间，超时会停止优化")
        performance_group_layout.addRow("最大处理时间:", self.rectpack_max_processing_time)

        # 批处理大小
        self.rectpack_batch_size = QSpinBox()
        self.rectpack_batch_size.setMinimum(10)
        self.rectpack_batch_size.setMaximum(1000)
        self.rectpack_batch_size.setSingleStep(10)
        self.rectpack_batch_size.setToolTip("批处理大小，影响内存使用和处理速度")
        performance_group_layout.addRow("批处理大小:", self.rectpack_batch_size)

        # 内存限制
        self.rectpack_memory_limit_mb = QSpinBox()
        self.rectpack_memory_limit_mb.setMinimum(256)
        self.rectpack_memory_limit_mb.setMaximum(8192)
        self.rectpack_memory_limit_mb.setSingleStep(256)
        self.rectpack_memory_limit_mb.setSuffix(" MB")
        self.rectpack_memory_limit_mb.setToolTip("内存使用限制")
        performance_group_layout.addRow("内存限制:", self.rectpack_memory_limit_mb)

        # 启用并行处理
        self.rectpack_enable_parallel = QCheckBox("启用并行处理")
        self.rectpack_enable_parallel.setToolTip("启用后，使用多线程加速处理（实验性功能）")
        performance_group_layout.addRow(self.rectpack_enable_parallel)

        performance_layout.addWidget(performance_group)

        # RectPack调试参数组
        debug_group = QGroupBox("RectPack调试参数")
        debug_group_layout = QFormLayout(debug_group)

        # 调试模式
        self.rectpack_debug_mode = QCheckBox("启用调试模式")
        self.rectpack_debug_mode.setToolTip("启用后，输出详细的调试信息")
        debug_group_layout.addRow(self.rectpack_debug_mode)

        # 日志级别
        self.rectpack_log_level = QComboBox()
        self.rectpack_log_level.addItems([
            "无日志",
            "基础日志 (推荐)",
            "详细日志",
            "调试日志"
        ])
        self.rectpack_log_level.setToolTip("选择日志输出级别")
        debug_group_layout.addRow("日志级别:", self.rectpack_log_level)

        # 保存中间结果
        self.rectpack_save_intermediate_results = QCheckBox("保存中间结果")
        self.rectpack_save_intermediate_results.setToolTip("保存算法执行过程中的中间结果，用于调试")
        debug_group_layout.addRow(self.rectpack_save_intermediate_results)

        # 启用可视化
        self.rectpack_visualization_enabled = QCheckBox("启用可视化")
        self.rectpack_visualization_enabled.setToolTip("生成布局可视化图片")
        debug_group_layout.addRow(self.rectpack_visualization_enabled)

        performance_layout.addWidget(debug_group)

        tab_widget.addTab(performance_tab, "性能调试")

        # 4. 应用程序设置选项卡 - 包含Photoshop和图库索引设置
        app_tab = QWidget()
        app_layout = QFormLayout(app_tab)

        # Photoshop设置组
        ps_group = QGroupBox("Photoshop设置")
        ps_layout = QFormLayout(ps_group)

        # 使用Photoshop设置
        self.use_photoshop = QCheckBox("使用Photoshop处理图像")
        self.use_photoshop.setToolTip("开启后，使用Photoshop进行图像处理和画布生成")
        ps_layout.addRow(self.use_photoshop)

        # 自动启动Photoshop设置
        self.auto_start_photoshop = QCheckBox("自动启动Photoshop")
        self.auto_start_photoshop.setToolTip("开启后，程序会自动启动Photoshop应用程序")
        ps_layout.addRow(self.auto_start_photoshop)

        # 保存格式设置
        self.save_format = QComboBox()
        self.save_format.addItems(['TIFF', 'JPEG'])
        self.save_format.setToolTip("选择图像保存格式")
        ps_layout.addRow("保存格式:", self.save_format)

        # 压缩方式设置
        self.compression = QComboBox()
        self.compression.addItems(['LZW', 'ZIP', 'JPEG'])
        self.compression.setToolTip("选择图像压缩方式")
        ps_layout.addRow("压缩方式:", self.compression)

        app_layout.addWidget(ps_group)

        # 图库索引设置组
        db_scan_group = QGroupBox("图库索引设置")
        db_scan_layout = QFormLayout(db_scan_group)

        # 图库索引快速模式开关
        self.is_db_scan_fast = QCheckBox("使用快速模式索引图库")
        self.is_db_scan_fast.setToolTip("开启后，索引图库时仅获取文件路径和名称信息，不读取图片内容，提高索引效率")
        db_scan_layout.addRow(self.is_db_scan_fast)

        # 添加说明标签
        db_scan_description = QLabel(
            "• 快速模式：仅获取文件路径和名称，索引速度快，内存占用少\n"
            "• 完整模式：读取图片内容获取尺寸信息，索引较慢但信息完整"
        )
        db_scan_description.setWordWrap(True)
        db_scan_description.setStyleSheet("color: #666; font-size: 11px;")
        db_scan_layout.addRow(db_scan_description)

        app_layout.addWidget(db_scan_group)

        tab_widget.addTab(app_tab, "应用程序")

        # 5. 测试和系统选项卡 - 包含测试模式和系统设置
        system_tab = QWidget()
        system_layout = QFormLayout(system_tab)

        # 测试模式设置组
        test_mode_group = QGroupBox("测试模式设置")
        test_mode_layout = QFormLayout(test_mode_group)

        # 测试模式开关
        self.is_test_mode = QCheckBox("启用测试模式")
        self.is_test_mode.setToolTip("开启后，使用色块替代图片，不启动Photoshop，提高测试效率")
        test_mode_layout.addRow(self.is_test_mode)

        # 测试全部数据开关
        self.is_test_all_data = QCheckBox("测试全部数据")
        self.is_test_all_data.setToolTip("开启后，测试表格里的全部图片数据，不检测是否有图片路径")
        test_mode_layout.addRow(self.is_test_all_data)

        # 添加说明标签
        test_mode_description = QLabel(
            "• 测试模式：使用色块替代图片，快速验证布局效果\n"
            "• 测试全部数据：包含未入库的图片数据，用于完整性测试\n"
            "• 测试模式使用cm直接转px，无需设置缩小比率"
        )
        test_mode_description.setWordWrap(True)
        test_mode_description.setStyleSheet("color: #666; font-size: 11px;")
        test_mode_layout.addRow(test_mode_description)

        system_layout.addWidget(test_mode_group)

        # Supabase心跳同步设置组
        heart_rate_group = QGroupBox("Supabase心跳同步设置")
        heart_rate_layout = QFormLayout(heart_rate_group)

        # 开启心跳同步开关
        self.is_heart_rate = QCheckBox("开启Supabase心跳同步")
        self.is_heart_rate.setToolTip("开启后，程序将定期从Supabase同步最新配置")
        heart_rate_layout.addRow(self.is_heart_rate)

        # 心跳同步间隔时间
        self.heart_rate_time = QSpinBox()
        self.heart_rate_time.setMinimum(30)  # 最小30秒
        self.heart_rate_time.setMaximum(86400)  # 最大24小时
        self.heart_rate_time.setSingleStep(30)  # 步长30秒
        self.heart_rate_time.setSuffix(" 秒")  # 单位秒
        self.heart_rate_time.setToolTip("设置心跳同步的间隔时间，范围：30秒-24小时")
        heart_rate_layout.addRow("同步间隔时间:", self.heart_rate_time)

        # 添加说明标签
        heart_rate_description = QLabel(
            "开启心跳同步后，程序将定期从Supabase云端同步最新的配置参数，\n"
            "包括画布设置、算法参数、模式开关等，确保配置始终保持最新状态。"
        )
        heart_rate_description.setWordWrap(True)
        heart_rate_description.setStyleSheet("color: #666; font-size: 11px;")
        heart_rate_layout.addRow(heart_rate_description)

        system_layout.addWidget(heart_rate_group)

        tab_widget.addTab(system_tab, "测试系统")

        layout.addWidget(tab_widget)

        # 添加按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def _init_canvas_ui(self, canvas_layout):
        """初始化画布设置UI"""
        # 直接使用传入的布局而不是创建新的QGroupBox
        # 最大高度 (厘米)
        self.max_height = QSpinBox()
        self.max_height.setMinimum(100)  # 最小1米
        self.max_height.setMaximum(50000)  # 最大500米
        self.max_height.setSingleStep(100)  # 步长1米
        self.max_height.setSuffix(" cm")  # 单位厘米
        canvas_layout.addRow("最大高度:", self.max_height)

        # PPI (像素每英寸)
        self.ppi = QSpinBox()
        self.ppi.setMinimum(36)  # 最小36 PPI
        self.ppi.setMaximum(600)  # 最大600 PPI
        self.ppi.setSingleStep(6)  # 步长6 PPI
        canvas_layout.addRow("分辨率 (PPI):", self.ppi)

        # 图像间距 (厘米)
        self.image_spacing = QDoubleSpinBox()
        self.image_spacing.setMinimum(0.0)  # 最小0厘米
        self.image_spacing.setMaximum(10.0)  # 最大10厘米
        self.image_spacing.setSingleStep(0.1)  # 步长0.1厘米
        self.image_spacing.setDecimals(1)  # 精度0.1厘米
        self.image_spacing.setSuffix(" cm")  # 单位厘米
        canvas_layout.addRow("图像间距:", self.image_spacing)

        # 水平扩展 (厘米)
        self.horizontal_expansion = QDoubleSpinBox()
        self.horizontal_expansion.setMinimum(0.0)  # 最小0厘米
        self.horizontal_expansion.setMaximum(10.0)  # 最大10厘米
        self.horizontal_expansion.setSingleStep(0.1)  # 步长0.1厘米
        self.horizontal_expansion.setDecimals(1)  # 精度0.1厘米
        self.horizontal_expansion.setSuffix(" cm")  # 单位厘米
        canvas_layout.addRow("水平扩展:", self.horizontal_expansion)

    def load_settings(self):
        """加载设置"""
        try:
            # 画布设置
            canvas_settings = self.config_manager.get_canvas_settings()
            self.max_height.setValue(int(canvas_settings['max_height_cm']))
            self.ppi.setValue(int(canvas_settings['ppi']))
            self.image_spacing.setValue(float(canvas_settings['image_spacing_cm']))
            self.horizontal_expansion.setValue(float(canvas_settings['horizontal_expansion_cm']))

            # RectPack算法设置
            rectpack_settings = self.config_manager.get_rectpack_settings()

            # 基础设置
            # self.use_rectpack_algorithm.setChecked(rectpack_settings.get('use_rectpack_algorithm', False))
            self.rectpack_rotation_enabled.setChecked(rectpack_settings.get('rectpack_rotation_enabled', True))
            self.rectpack_sort_strategy.setCurrentIndex(rectpack_settings.get('rectpack_sort_strategy', 0))
            self.rectpack_pack_algorithm.setCurrentIndex(rectpack_settings.get('rectpack_pack_algorithm', 0))

            # 高级设置
            self.rectpack_bin_selection_strategy.setCurrentIndex(rectpack_settings.get('rectpack_bin_selection_strategy', 0))
            self.rectpack_split_heuristic.setCurrentIndex(rectpack_settings.get('rectpack_split_heuristic', 0))
            self.rectpack_free_rect_choice.setCurrentIndex(rectpack_settings.get('rectpack_free_rect_choice', 0))

            # 优化设置
            self.rectpack_enable_optimization.setChecked(rectpack_settings.get('rectpack_enable_optimization', True))
            self.rectpack_optimization_iterations.setValue(rectpack_settings.get('rectpack_optimization_iterations', 5))
            self.rectpack_min_utilization_threshold.setValue(rectpack_settings.get('rectpack_min_utilization_threshold', 85.0))
            self.rectpack_rotation_penalty.setValue(rectpack_settings.get('rectpack_rotation_penalty', 0.05))
            self.rectpack_aspect_ratio_preference.setValue(rectpack_settings.get('rectpack_aspect_ratio_preference', 1.0))

            # 性能设置
            self.rectpack_max_processing_time.setValue(rectpack_settings.get('rectpack_max_processing_time', 300))
            self.rectpack_batch_size.setValue(rectpack_settings.get('rectpack_batch_size', 100))
            self.rectpack_memory_limit_mb.setValue(rectpack_settings.get('rectpack_memory_limit_mb', 1024))
            self.rectpack_enable_parallel.setChecked(rectpack_settings.get('rectpack_enable_parallel', False))

            # 调试设置
            self.rectpack_debug_mode.setChecked(rectpack_settings.get('rectpack_debug_mode', False))
            self.rectpack_log_level.setCurrentIndex(rectpack_settings.get('rectpack_log_level', 1))
            self.rectpack_save_intermediate_results.setChecked(rectpack_settings.get('rectpack_save_intermediate_results', False))
            self.rectpack_visualization_enabled.setChecked(rectpack_settings.get('rectpack_visualization_enabled', False))

            # 排列设置
            # 获取精确查询图案全称设置，默认为关闭
            exact_pattern_search = self.config_manager.get('exact_pattern_search', False)
            self.exact_pattern_search.setChecked(exact_pattern_search)

            # 获取表格模式设置，默认为标准模式
            is_standard_mode = self.config_manager.get('is_standard_mode', True)
            self.is_standard_mode.setChecked(is_standard_mode)

            # 获取模糊查询设置，默认为关闭
            is_fuzzy_query = self.config_manager.get('is_fuzzy_query', False)
            self.is_fuzzy_query.setChecked(is_fuzzy_query)

            # 注意：已移除Tetris算法设置加载，现在只使用RectPack算法

            # Photoshop设置
            ps_settings = self.config_manager.get_photoshop_settings()
            self.use_photoshop.setChecked(ps_settings['use_photoshop'])
            self.auto_start_photoshop.setChecked(ps_settings['auto_start_photoshop'])

            # 保存设置 - 找到相应的索引
            save_format = ps_settings['save_format']
            save_format_index = self.save_format.findText(save_format)
            if save_format_index >= 0:
                self.save_format.setCurrentIndex(save_format_index)

            # 压缩设置 - 找到相应的索引
            compression = ps_settings['compression']
            compression_index = self.compression.findText(compression)
            if compression_index >= 0:
                self.compression.setCurrentIndex(compression_index)

            # 图库索引设置
            is_db_scan_fast = self.config_manager.get_db_scan_fast()
            self.is_db_scan_fast.setChecked(is_db_scan_fast)

            # 测试模式设置
            test_mode_settings = self.config_manager.get_test_mode_settings()
            self.is_test_mode.setChecked(test_mode_settings['is_test_mode'])
            self.is_test_all_data.setChecked(test_mode_settings.get('is_test_all_data', False))

            # Supabase心跳同步设置
            heart_rate_settings = self.config_manager.get_heart_rate_settings()
            self.is_heart_rate.setChecked(heart_rate_settings['is_heart_rate'])
            self.heart_rate_time.setValue(heart_rate_settings['heart_rate_time'])
        except Exception as e:
            log.error(f"加载设置时出错: {str(e)}")
            # 显示错误消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "加载设置失败", f"无法加载设置: {str(e)}\n将使用默认值。")
            # 使用默认值
            self.max_height.setValue(1000)
            self.ppi.setValue(72)
            self.image_spacing.setValue(1.0)
            self.horizontal_expansion.setValue(0)
            self.use_photoshop.setChecked(True)
            self.auto_start_photoshop.setChecked(True)
            self.save_format.setCurrentText("TIFF")
            self.compression.setCurrentText("LZW")

    def get_settings(self) -> Dict[str, Any]:
        """获取设置值

        Returns:
            设置字典
        """
        return {
            # 画布设置
            'max_height_cm': self.max_height.value(),
            'ppi': self.ppi.value(),
            'image_spacing_cm': self.image_spacing.value(),
            'horizontal_expansion_cm': self.horizontal_expansion.value(),

            # RectPack算法设置
            # 'use_rectpack_algorithm': self.use_rectpack_algorithm.isChecked(),
            'rectpack_rotation_enabled': self.rectpack_rotation_enabled.isChecked(),
            'rectpack_sort_strategy': self.rectpack_sort_strategy.currentIndex(),
            'rectpack_pack_algorithm': self.rectpack_pack_algorithm.currentIndex(),

            # 排列设置
            'exact_pattern_search': self.exact_pattern_search.isChecked(),
            'is_standard_mode': self.is_standard_mode.isChecked(),
            'is_fuzzy_query': self.is_fuzzy_query.isChecked(),

            # 注意：已移除Tetris算法设置获取，现在只使用RectPack算法

            # Photoshop设置
            'use_photoshop': self.use_photoshop.isChecked(),
            'auto_start_photoshop': self.auto_start_photoshop.isChecked(),
            'save_format': self.save_format.currentText(),
            'compression': self.compression.currentText(),

            # 图库索引设置
            'is_db_scan_fast': self.is_db_scan_fast.isChecked(),

            # 测试模式设置
            'is_test_mode': self.is_test_mode.isChecked(),
            'is_test_all_data': self.is_test_all_data.isChecked(),

            # Supabase心跳同步设置
            'is_heart_rate': self.is_heart_rate.isChecked(),
            'heart_rate_time': self.heart_rate_time.value()
        }

    def accept(self):
        """确认设置"""
        try:
            # 保存设置，使用save_settings方法
            self.save_settings()
            # 通知主窗口设置已更改
            from PyQt6.QtWidgets import QApplication
            QApplication.processEvents()  # 确保UI更新
            super().accept()
        except Exception as e:
            log.error(f"保存设置时出错: {str(e)}")
            # 显示错误消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "保存设置失败", f"无法保存设置: {str(e)}")
            # 尝试关闭对话框
            super().accept()

    def save_settings(self):
        """保存设置"""
        try:
            # 构建要更新的设置字典
            settings = self.get_settings()
            # 使用配置管理器的update方法批量更新
            success = self.config_manager.update(settings)
            if not success:
                log.warning("设置可能未完全保存")
        except Exception as e:
            log.error(f"保存设置异常: {str(e)}")
            # 尝试单独保存每个设置项
            try:
                # 画布设置
                self.config_manager.set('max_height_cm', self.max_height.value())
                self.config_manager.set('ppi', self.ppi.value())
                self.config_manager.set('image_spacing_cm', self.image_spacing.value())
                self.config_manager.set('horizontal_expansion_cm', self.horizontal_expansion.value())

                # RectPack算法设置
                rectpack_settings = {
                    # 基础设置
                    # 'use_rectpack_algorithm': self.use_rectpack_algorithm.isChecked(),
                    'rectpack_rotation_enabled': self.rectpack_rotation_enabled.isChecked(),
                    'rectpack_sort_strategy': self.rectpack_sort_strategy.currentIndex(),
                    'rectpack_pack_algorithm': self.rectpack_pack_algorithm.currentIndex(),

                    # 高级设置
                    'rectpack_bin_selection_strategy': self.rectpack_bin_selection_strategy.currentIndex(),
                    'rectpack_split_heuristic': self.rectpack_split_heuristic.currentIndex(),
                    'rectpack_free_rect_choice': self.rectpack_free_rect_choice.currentIndex(),

                    # 优化设置
                    'rectpack_enable_optimization': self.rectpack_enable_optimization.isChecked(),
                    'rectpack_optimization_iterations': self.rectpack_optimization_iterations.value(),
                    'rectpack_min_utilization_threshold': self.rectpack_min_utilization_threshold.value(),
                    'rectpack_rotation_penalty': self.rectpack_rotation_penalty.value(),
                    'rectpack_aspect_ratio_preference': self.rectpack_aspect_ratio_preference.value(),

                    # 性能设置
                    'rectpack_max_processing_time': self.rectpack_max_processing_time.value(),
                    'rectpack_batch_size': self.rectpack_batch_size.value(),
                    'rectpack_memory_limit_mb': self.rectpack_memory_limit_mb.value(),
                    'rectpack_enable_parallel': self.rectpack_enable_parallel.isChecked(),

                    # 调试设置
                    'rectpack_debug_mode': self.rectpack_debug_mode.isChecked(),
                    'rectpack_log_level': self.rectpack_log_level.currentIndex(),
                    'rectpack_save_intermediate_results': self.rectpack_save_intermediate_results.isChecked(),
                    'rectpack_visualization_enabled': self.rectpack_visualization_enabled.isChecked()
                }
                self.config_manager.set_rectpack_settings(rectpack_settings)

                # 排列设置
                self.config_manager.set('exact_pattern_search', self.exact_pattern_search.isChecked())
                self.config_manager.set('is_standard_mode', self.is_standard_mode.isChecked())
                self.config_manager.set('is_fuzzy_query', self.is_fuzzy_query.isChecked())

                # Photoshop设置
                self.config_manager.set('use_photoshop', self.use_photoshop.isChecked())
                self.config_manager.set('auto_start_photoshop', self.auto_start_photoshop.isChecked())
                self.config_manager.set('save_format', self.save_format.currentText())
                self.config_manager.set('compression', self.compression.currentText())

                # 图库索引设置
                self.config_manager.set('is_db_scan_fast', self.is_db_scan_fast.isChecked())

                # 测试模式设置
                self.config_manager.set('is_test_mode', self.is_test_mode.isChecked())
                self.config_manager.set('is_test_all_data', self.is_test_all_data.isChecked())

                # Supabase心跳同步设置
                self.config_manager.set('is_heart_rate', self.is_heart_rate.isChecked())
                self.config_manager.set('heart_rate_time', self.heart_rate_time.value())
            except Exception as e2:
                log.error(f"备用保存设置方法也失败: {str(e2)}")
                raise
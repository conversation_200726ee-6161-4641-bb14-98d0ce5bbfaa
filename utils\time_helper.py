#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
时间处理辅助工具
解决time模块作用域问题，提供统一的时间处理接口

遵循原则：
- DRY: 避免重复的时间处理代码
- KISS: 简单直接的接口
- SOLID: 单一职责原则
- YAGNI: 只实现需要的功能
"""

import logging
from typing import Optional

# 配置日志
log = logging.getLogger(__name__)


class TimeHelper:
    """时间处理辅助类"""

    @staticmethod
    def get_current_time_string(format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """
        获取当前时间字符串，使用多种方式确保兼容性

        Args:
            format_str: 时间格式字符串

        Returns:
            str: 格式化的时间字符串
        """
        try:
            # 方法1: 使用time模块
            import time as time_module
            return time_module.strftime(format_str)
        except (ImportError, NameError, AttributeError) as e:
            log.warning(f"time模块方法失败: {str(e)}")

        try:
            # 方法2: 使用datetime模块
            from datetime import datetime
            return datetime.now().strftime(format_str)
        except (ImportError, AttributeError) as e:
            log.warning(f"datetime模块方法失败: {str(e)}")

        try:
            # 方法3: 使用time模块的备用方法
            import time
            return time.strftime(format_str)
        except Exception as e:
            log.error(f"所有时间获取方法都失败: {str(e)}")

        # 最后的备选方案
        return 'Unknown Time'

    @staticmethod
    def get_timestamp() -> float:
        """
        获取当前时间戳，使用多种方式确保兼容性

        Returns:
            float: 时间戳
        """
        try:
            # 方法1: 使用time模块
            import time as time_module
            return time_module.time()
        except (ImportError, NameError, AttributeError) as e:
            log.warning(f"time模块时间戳获取失败: {str(e)}")

        try:
            # 方法2: 使用datetime模块
            from datetime import datetime
            return datetime.now().timestamp()
        except (ImportError, AttributeError) as e:
            log.warning(f"datetime模块时间戳获取失败: {str(e)}")

        try:
            # 方法3: 使用time模块的备用方法
            import time
            return time.time()
        except Exception as e:
            log.error(f"所有时间戳获取方法都失败: {str(e)}")

        # 最后的备选方案
        return 0.0

    @staticmethod
    def sleep(seconds: float) -> bool:
        """
        休眠指定秒数，使用多种方式确保兼容性

        Args:
            seconds: 休眠秒数

        Returns:
            bool: 是否成功休眠
        """
        # 参数验证
        if seconds < 0:
            log.warning(f"休眠时间不能为负数: {seconds}，将使用0")
            seconds = 0

        try:
            # 方法1: 使用time模块
            import time as time_module
            time_module.sleep(seconds)
            return True
        except (ImportError, NameError, AttributeError) as e:
            log.warning(f"time模块休眠失败: {str(e)}")

        try:
            # 方法2: 使用time模块的备用方法
            import time
            time.sleep(seconds)
            return True
        except Exception as e:
            log.error(f"所有休眠方法都失败: {str(e)}")

        return False

    @staticmethod
    def calculate_elapsed_time(start_time: Optional[float], end_time: Optional[float] = None) -> float:
        """
        计算经过的时间

        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳，如果为None则使用当前时间

        Returns:
            float: 经过的时间（秒）
        """
        if start_time is None:
            return 0.0

        if end_time is None:
            end_time = TimeHelper.get_timestamp()

        return max(0.0, end_time - start_time)


# 全局实例，方便使用
time_helper = TimeHelper()

# 便捷函数
def get_current_time_string(format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """获取当前时间字符串的便捷函数"""
    return time_helper.get_current_time_string(format_str)

def get_timestamp() -> float:
    """获取当前时间戳的便捷函数"""
    return time_helper.get_timestamp()

def safe_sleep(seconds: float) -> bool:
    """安全休眠的便捷函数"""
    return time_helper.sleep(seconds)

def calculate_elapsed_time(start_time: Optional[float], end_time: Optional[float] = None) -> float:
    """计算经过时间的便捷函数"""
    return time_helper.calculate_elapsed_time(start_time, end_time)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
画布迭代管理器模块

提供画布迭代和参数优化的管理功能：
1. 管理画布迭代过程
2. 集成参数优化和迭代卡片生成
3. 提供UI交互接口
4. 保存和加载迭代结果
"""

import os
import sys
import logging
import time
import json
import threading
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from PyQt6.QtCore import QObject, pyqtSignal, QThread

# 导入自定义模块
from utils.canvas_optimizer import CanvasOptimizer
from utils.parallel_manager import ParallelManager
from ui.iteration_cards_dialog import IterationCardsDialog

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("CanvasIterationManager")

class CanvasIterationWorker(QThread):
    """画布迭代工作线程"""
    
    # 定义信号
    progress_signal = pyqtSignal(int)  # 进度信号
    log_signal = pyqtSignal(str)  # 日志信号
    finished_signal = pyqtSignal(list)  # 完成信号，传递迭代卡片列表
    error_signal = pyqtSignal(str)  # 错误信号
    
    def __init__(self, pattern_items, canvas_settings, iteration_params, parent=None):
        """
        初始化画布迭代工作线程
        
        Args:
            pattern_items: 图案项目列表
            canvas_settings: 画布设置
            iteration_params: 迭代参数
            parent: 父对象
        """
        super().__init__(parent)
        self.pattern_items = pattern_items
        self.canvas_settings = canvas_settings
        self.iteration_params = iteration_params
        self.optimizer = CanvasOptimizer()
        self.is_running = False
    
    def run(self):
        """运行线程"""
        self.is_running = True
        self.log_signal.emit("开始画布迭代...")
        
        try:
            # 获取迭代参数
            canvas_iteration_count = self.iteration_params.get('canvas_iteration_count', 5)
            canvas_iteration_time = self.iteration_params.get('canvas_iteration_time', 5)
            
            self.log_signal.emit(f"迭代参数: 每次迭代 {canvas_iteration_count} 组参数，共 {canvas_iteration_time} 次迭代")
            
            # 生成迭代卡片
            self.log_signal.emit("生成迭代卡片...")
            self.progress_signal.emit(10)
            
            # 生成第一批迭代卡片
            iteration_cards = self.optimizer.generate_iteration_cards(
                self.pattern_items, self.canvas_settings, self.iteration_params)
            
            self.progress_signal.emit(50)
            
            # 如果需要多次迭代，继续生成
            if canvas_iteration_time > 1 and self.is_running:
                self.log_signal.emit(f"继续迭代，剩余 {canvas_iteration_time - 1} 次...")
                
                # 计算每次迭代的进度增量
                progress_increment = 40 / (canvas_iteration_time - 1)
                current_progress = 50
                
                # 继续迭代
                for i in range(1, canvas_iteration_time):
                    if not self.is_running:
                        break
                    
                    self.log_signal.emit(f"第 {i+1}/{canvas_iteration_time} 次迭代...")
                    
                    # 生成新的迭代卡片
                    new_cards = self.optimizer.generate_iteration_cards(
                        self.pattern_items, self.canvas_settings, self.iteration_params)
                    
                    # 合并卡片并按利用率排序
                    iteration_cards.extend(new_cards)
                    iteration_cards.sort(key=lambda x: x['canvas_utilization'], reverse=True)
                    
                    # 只保留前10张卡片
                    if len(iteration_cards) > 10:
                        iteration_cards = iteration_cards[:10]
                    
                    # 更新进度
                    current_progress += progress_increment
                    self.progress_signal.emit(int(current_progress))
            
            self.log_signal.emit(f"迭代完成，生成了 {len(iteration_cards)} 张卡片")
            self.progress_signal.emit(100)
            
            # 发送完成信号
            self.finished_signal.emit(iteration_cards)
            
        except Exception as e:
            log.error(f"画布迭代失败: {str(e)}")
            self.error_signal.emit(f"画布迭代失败: {str(e)}")
        
        finally:
            self.is_running = False
    
    def stop(self):
        """停止线程"""
        self.is_running = False
        self.log_signal.emit("正在停止画布迭代...")
        self.wait()

class CanvasIterationManager(QObject):
    """画布迭代管理器"""
    
    # 定义信号
    progress_signal = pyqtSignal(int)  # 进度信号
    log_signal = pyqtSignal(str)  # 日志信号
    finished_signal = pyqtSignal(dict)  # 完成信号，传递选中的卡片
    error_signal = pyqtSignal(str)  # 错误信号
    
    def __init__(self, parent=None):
        """
        初始化画布迭代管理器
        
        Args:
            parent: 父对象
        """
        super().__init__(parent)
        self.parent = parent
        self.worker = None
        self.iteration_cards = []
        self.selected_card = None
    
    def start_iteration(self, pattern_items, canvas_settings, iteration_params):
        """
        开始画布迭代
        
        Args:
            pattern_items: 图案项目列表
            canvas_settings: 画布设置
            iteration_params: 迭代参数
        """
        
        # 创建工作线程
        self.worker = CanvasIterationWorker(pattern_items, canvas_settings, iteration_params, self)
        
        # 连接信号
        self.worker.progress_signal.connect(self.progress_signal)
        self.worker.log_signal.connect(self.log_signal)
        self.worker.error_signal.connect(self.error_signal)
        self.worker.finished_signal.connect(self._on_iteration_finished)
        
        # 启动线程
        self.worker.start()
        
        self.log_signal.emit("画布迭代已启动...")
    
    def stop_iteration(self):
        """停止画布迭代"""
        if self.worker and self.worker.is_running:
            self.worker.stop()
            self.log_signal.emit("画布迭代已停止")
    
    def _on_iteration_finished(self, iteration_cards):
        """
        迭代完成回调
        
        Args:
            iteration_cards: 迭代卡片列表
        """
        self.iteration_cards = iteration_cards
        
        # 显示迭代卡片对话框
        self.show_iteration_cards_dialog()
    
    def show_iteration_cards_dialog(self):
        """显示迭代卡片对话框"""
        if not self.iteration_cards:
            self.error_signal.emit("没有生成任何迭代卡片")
            return
        
        # 创建对话框
        dialog = IterationCardsDialog(self.iteration_cards, self.parent)
        
        # 连接信号
        dialog.card_selected.connect(self._on_card_selected)
        
        # 显示对话框
        result = dialog.exec()
        
        # 如果用户取消，发送错误信号
        if result == 0:
            self.error_signal.emit("用户取消了迭代卡片选择")
    
    def _on_card_selected(self, card):
        """
        卡片选择回调
        
        Args:
            card: 选中的卡片
        """
        self.selected_card = card
        
        # 发送完成信号
        self.finished_signal.emit(card)
        
        self.log_signal.emit(f"用户选择了利用率为 {card['canvas_utilization']:.4f}% 的卡片")
    
    def get_selected_card(self):
        """获取选中的卡片"""
        return self.selected_card
    
    def save_iteration_results(self, output_path):
        """
        保存迭代结果
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            # 创建结果对象
            result = {
                'iteration_cards': self.iteration_cards,
                'selected_card': self.selected_card,
                'timestamp': datetime.now().isoformat()
            }
            
            # 保存到文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            self.log_signal.emit(f"迭代结果已保存到: {output_path}")
            return True
            
        except Exception as e:
            log.error(f"保存迭代结果失败: {str(e)}")
            self.error_signal.emit(f"保存迭代结果失败: {str(e)}")
            return False
    
    def load_iteration_results(self, input_path):
        """
        加载迭代结果
        
        Args:
            input_path: 输入文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            # 从文件加载
            with open(input_path, 'r', encoding='utf-8') as f:
                result = json.load(f)
            
            # 更新迭代卡片和选中的卡片
            self.iteration_cards = result.get('iteration_cards', [])
            self.selected_card = result.get('selected_card')
            
            self.log_signal.emit(f"迭代结果已从 {input_path} 加载")
            return True
            
        except Exception as e:
            log.error(f"加载迭代结果失败: {str(e)}")
            self.error_signal.emit(f"加载迭代结果失败: {str(e)}")
            return False
